<?php
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // 添加updated_at字段到orders表
    $sql = "ALTER TABLE orders 
            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP 
            AFTER created_at";
    $pdo->exec($sql);
    
    // 将所有订单的updated_at设置为created_at
    $sql = "UPDATE orders SET updated_at = created_at WHERE updated_at IS NULL";
    $pdo->exec($sql);
    
    echo "成功添加orders表的updated_at字段！";
    
} catch(PDOException $e) {
    die("修复orders表失败: " . $e->getMessage());
}