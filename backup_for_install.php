<?php
// 此脚本用于备份数据库，供安装向导使用
// 不需要登录验证，直接执行即可生成备份文件

// 设置脚本可以长时间运行
set_time_limit(300);

// 引入配置文件
require_once 'config.php';

// 定义日志函数
function logBackupMessage($message) {
    $logFile = __DIR__ . '/数据库/backup_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    echo "<p>$message</p>";
}

try {
    echo "<h2>开始备份数据库</h2>";
    logBackupMessage('开始执行数据库备份（安装用）');
    
    $pdo = getDbConnection();
    
    // 设置导出文件名 - 使用固定名称，方便安装程序找到
    $filename = "install_database_backup.sql";
    $backup_dir = __DIR__ . '/数据库';

    // 确保备份目录存在
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
        logBackupMessage("创建备份目录: $backup_dir");
    }

    $backup_file = $backup_dir . '/' . $filename;
    $output = '';
    
    // 获取数据库名称
    $dbName = DB_NAME;
    
    // 添加创建数据库和使用数据库语句
    $output .= "-- 创建数据库\n";
    $output .= "CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
    $output .= "USE `{$dbName}`;\n\n";

    // 获取所有视图
    $views = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE LIKE 'VIEW'")->fetchAll(PDO::FETCH_COLUMN);
    
    // 删除所有视图
    $output .= "\n-- 删除所有视图\n";
    foreach ($views as $view) {
        $output .= "DROP VIEW IF EXISTS `{$view}`;\n";
    }
    
    // 删除所有存储过程
    $output .= "\n-- 删除所有存储过程\n";
    $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($procedures as $procedure) {
        $output .= "DROP PROCEDURE IF EXISTS `{$procedure}`;\n";
    }
    
    // 删除所有函数
    $output .= "\n-- 删除所有函数\n";
    $functions = $pdo->query("SHOW FUNCTION STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($functions as $function) {
        $output .= "DROP FUNCTION IF EXISTS `{$function}`;\n";
    }
    
    // 删除所有事件
    $output .= "\n-- 删除所有事件\n";
    $events = $pdo->query("SHOW EVENTS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($events as $event) {
        $output .= "DROP EVENT IF EXISTS `{$event}`;\n";
    }
    
    // 删除所有触发器
    $output .= "\n-- 删除所有触发器\n";
    $triggers = $pdo->query("SHOW TRIGGERS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($triggers as $trigger) {
        $output .= "DROP TRIGGER IF EXISTS `{$trigger}`;\n";
    }
    
    // 获取所有表
    $allTables = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE = 'BASE TABLE'")->fetchAll(PDO::FETCH_COLUMN);
    
    // 定义表的创建顺序，确保外键约束不会出错
    $orderedTables = array(
        'users',      // 基础表，无外键依赖
        'categories', // 基础表，无外键依赖
        'products',   // 依赖于categories表（外键约束）
        'orders',     // 依赖于users表
        'order_items', // 依赖于orders和products表
        'cart_items'  // 依赖于users和products表
    );
    
    // 确保所有表都被包含，即使不在预定义顺序中的表
    $tables = array_merge($orderedTables, array_diff($allTables, $orderedTables));
    
    // 验证所有表是否存在
    $tables = array_intersect($tables, $allTables);
    
    logBackupMessage("找到 " . count($tables) . " 个表需要备份");
    
    foreach ($tables as $table) {
        logBackupMessage("正在备份表: {$table}");
        
        // 添加删除表和创建表语句
        $output .= "\n-- 导出 {$table} 表结构\n";
        $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
        
        $create_table = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch(PDO::FETCH_ASSOC);
        $output .= $create_table['Create Table'] . ";\n\n";

        // 获取表数据
        $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $output .= "-- 导出 {$table} 表数据\n";
            $columns = array_keys($rows[0]);
            $output .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = $pdo->quote($value);
                    }
                }
                $values[] = "(" . implode(", ", $rowValues) . ")";
            }
            $output .= implode(",\n", $values) . ";\n";
        }
        
        $output .= "\n";
    }

    // 写入文件
    if (file_put_contents($backup_file, $output)) {
        logBackupMessage("数据库备份成功！文件已保存在：{$backup_dir}/{$filename}");
        echo "<div class='alert alert-success'>数据库备份成功！文件已保存在：{$backup_dir}/{$filename}</div>";
    } else {
        throw new Exception("写入备份文件失败，请检查目录权限");
    }
} catch (Exception $e) {
    $errorMessage = '备份失败：' . $e->getMessage();
    logBackupMessage($errorMessage);
    echo "<div class='alert alert-danger'>{$errorMessage}</div>";
}

echo "<p><a href='index.php' class='btn btn-primary'>返回首页</a></p>";
?>