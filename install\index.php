<?php
session_start();

// 检查是否已经安装
if (file_exists('../config.php')) {
    require_once '../config.php';
    if (defined('DB_INSTALLED') && DB_INSTALLED === true) {
        // 已安装，显示重新安装确认
        if (!isset($_GET['force'])) {
            ?>
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>系统已安装</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-warning">⚠️ 系统已安装</h3>
                                    <p>检测到系统已经安装完成。</p>
                                    <p>如果需要重新安装，将会清除所有现有数据。</p>
                                    <div class="mt-4">
                                        <a href="../index.php" class="btn btn-primary me-2">返回网站</a>
                                        <a href="?force=1" class="btn btn-danger">强制重新安装</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            <?php
            exit;
        }
    }
}

// 获取当前步骤
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$maxStep = 5;

// 步骤名称
$stepNames = [
    1 => '环境检查',
    2 => '数据库配置',
    3 => '数据库连接测试',
    4 => '安装数据库',
    5 => '完成安装'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件商城 - 安装向导</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .install-header {
            background: linear-gradient(135deg, #e4393c, #c81623);
            color: white;
            padding: 2rem 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step-item {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step-current .step-number {
            background: #e4393c;
            color: white;
        }
        .step-completed .step-number {
            background: #28a745;
            color: white;
        }
        .step-pending .step-number {
            background: #e9ecef;
            color: #6c757d;
        }
        .install-content {
            min-height: 400px;
            padding: 2rem;
        }
        .requirement-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }
        .requirement-status {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <!-- 安装头部 -->
    <div class="install-header">
        <div class="container text-center">
            <h1><i class="bi bi-gear-fill"></i> 软件商城安装向导</h1>
            <p class="mb-0">欢迎使用软件商城系统，请按照向导完成安装</p>
        </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="container">
        <div class="step-indicator">
            <?php for ($i = 1; $i <= $maxStep; $i++): ?>
                <div class="step-item <?php echo $i < $step ? 'step-completed' : ($i == $step ? 'step-current' : 'step-pending'); ?>">
                    <div class="step-number">
                        <?php if ($i < $step): ?>
                            <i class="bi bi-check"></i>
                        <?php else: ?>
                            <?php echo $i; ?>
                        <?php endif; ?>
                    </div>
                    <span><?php echo $stepNames[$i]; ?></span>
                </div>
                <?php if ($i < $maxStep): ?>
                    <div class="mx-2">→</div>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
    </div>

    <!-- 安装内容 -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body install-content">
                        <?php
                        switch ($step) {
                            case 1:
                                include 'step1_requirements.php';
                                break;
                            case 2:
                                include 'step2_database.php';
                                break;
                            case 3:
                                include 'step3_test.php';
                                break;
                            case 4:
                                include 'step4_install.php';
                                break;
                            case 5:
                                include 'step5_complete.php';
                                break;
                            default:
                                echo '<p>无效的安装步骤</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
