-- 软件商城数据库备份
-- 生成时间: 2025-07-07 21:25:00
-- 数据库版本: MySQL

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

-- 表结构: cart_items
DROP TABLE IF EXISTS `cart_items`;
CREATE TABLE `cart_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `cart_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `cart_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

-- 表结构: categories
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

-- 表数据: categories
INSERT INTO `categories` (`id`, `name`, `description`, `created_at`, `is_active`) VALUES
('1', '办公软件', '办公软件和生产力工具', '2025-07-07 19:33:45', '1'),
('2', '开发工具', '编程开发相关软件', '2025-07-07 19:33:45', '1'),
('3', '设计软件', '图形设计和创意软件', '2025-07-07 19:33:46', '1'),
('4', '系统工具', '系统优化和管理工具', '2025-07-07 19:33:46', '1'),
('5', '安全软件', '杀毒软件和安全工具', '2025-07-07 19:33:46', '1'),
('6', '多媒体', '音视频处理软件', '2025-07-07 19:33:46', '1');

-- 表结构: order_items
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 表数据: order_items
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `price`) VALUES
('1', '1', '1', '1', '899.00');

-- 表结构: orders
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','shipped','completed','cancelled') DEFAULT 'pending',
  `shipping_address` text NOT NULL,
  `payment_method` enum('alipay','wechat') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 表数据: orders
INSERT INTO `orders` (`id`, `user_id`, `total_amount`, `status`, `shipping_address`, `payment_method`, `created_at`, `updated_at`) VALUES
('1', '1', '899.00', 'paid', '45878787', 'alipay', '2025-07-07 19:44:32', '2025-07-07 19:44:34');

-- 表结构: product_images
DROP TABLE IF EXISTS `product_images`;
CREATE TABLE `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `is_primary` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 表数据: product_images
INSERT INTO `product_images` (`id`, `product_id`, `image_url`, `is_primary`, `created_at`) VALUES
('1', '7', 'uploads/products/product_686bc97bb8011.png', '1', '2025-07-07 21:19:55');

-- 表结构: products
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT '0',
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_top` tinyint(1) NOT NULL DEFAULT '0',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_top_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;

-- 表数据: products
INSERT INTO `products` (`id`, `category_id`, `name`, `description`, `price`, `stock`, `image_url`, `is_active`, `is_top`, `sort_order`, `created_at`, `updated_at`, `last_top_time`) VALUES
('1', '1', 'Microsoft Office 2021', '微软办公套件，包含Word、Excel、PowerPoint等', '899.00', '99', 'https://via.placeholder.com/300x200?text=Office+2021', '1', '1', '0', '2025-07-07 19:33:46', '2025-07-07 21:17:23', '2025-07-07 21:17:23'),
('2', '3', 'Adobe Photoshop 2024', '专业图像编辑软件', '1299.00', '50', 'https://via.placeholder.com/300x200?text=Photoshop', '1', '0', '0', '2025-07-07 19:33:46', '2025-07-07 19:33:46', NULL),
('3', '2', 'Visual Studio Code', '免费的代码编辑器', '0.00', '999', 'https://via.placeholder.com/300x200?text=VS+Code', '1', '0', '0', '2025-07-07 19:33:46', '2025-07-07 19:33:46', NULL),
('4', '4', 'CCleaner Pro', '系统清理和优化工具', '199.00', '200', 'https://via.placeholder.com/300x200?text=CCleaner', '1', '0', '0', '2025-07-07 19:33:46', '2025-07-07 19:33:46', NULL),
('5', '5', 'Norton Antivirus', '诺顿杀毒软件', '299.00', '150', 'https://via.placeholder.com/300x200?text=Norton', '1', '0', '0', '2025-07-07 19:33:46', '2025-07-07 19:33:46', NULL),
('6', '6', 'Adobe Premiere Pro', '专业视频编辑软件', '1599.00', '30', 'https://via.placeholder.com/300x200?text=Premiere', '1', '0', '0', '2025-07-07 19:33:46', '2025-07-07 19:33:46', NULL),
('7', '6', '111', '11', '100.00', '500', 'uploads/products/product_686bc97bb8011.png', '1', '0', '0', '2025-07-07 21:19:55', '2025-07-07 21:19:55', NULL);

-- 表结构: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 表数据: users
INSERT INTO `users` (`id`, `username`, `password`, `email`, `nickname`, `is_admin`, `is_active`, `created_at`) VALUES
('1', 'admin', '$2y$10$UZ5FlF08VcPlVI.nPKIU6.slTxmrigmM3pO./sn/5O0J1lOdEyFg.', NULL, '管理员', '1', '1', '2025-05-29 20:52:24');

SET FOREIGN_KEY_CHECKS = 1;
