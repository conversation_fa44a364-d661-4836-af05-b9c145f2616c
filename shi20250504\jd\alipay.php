<?php
require_once 'config.php';
require_once 'alipay_config.php';

/**
 * 生成支付宝支付二维码
 * @param int $orderId 订单ID
 * @param float $amount 支付金额
 * @param string $subject 订单标题
 * @return string 支付二维码URL
 */
function generateAlipayQRCode($orderId, $amount, $subject) {
    require_once 'alipay_sdk.php';
    
    // 实例化支付宝SDK
    $alipaySDK = new AlipaySDK();
    
    // 构建支付参数
    $params = [
        'out_trade_no' => $orderId,
        'total_amount' => $amount,
        'subject' => $subject,
    ];
    
    // 调用SDK生成支付二维码
    $qrCodeUrl = $alipaySDK->generateQRCode($params);
    
    // 如果生成失败，返回默认图片
    if (!$qrCodeUrl) {
        $qrCodeUrl = 'https://via.placeholder.com/200x200?text=支付宝支付生成失败';
    }
    
    // 记录支付请求日志
    logAlipayRequest($orderId, $params);
    
    return $qrCodeUrl;
}

/**
 * 验证支付宝支付状态
 * @param int $orderId 订单ID
 * @return bool 是否支付成功
 */
function verifyAlipayPayment($orderId) {
    // 在实际应用中，这里应该调用支付宝SDK查询支付状态
    // 这里仅作为示例，始终返回true表示支付成功
    return true;
}

/**
 * 记录支付宝支付请求日志
 * @param int $orderId 订单ID
 * @param array $params 支付参数
 */
function logAlipayRequest($orderId, $params) {
    // 在实际应用中，这里应该记录支付请求日志
    // 可以记录到数据库或日志文件中
}