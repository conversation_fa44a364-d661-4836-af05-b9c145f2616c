/**
 * 管理员页面对话框组件
 * 基于dialog.js，专为管理员页面定制
 */

// 确保全局dialog对象已加载
if (typeof dialog === 'undefined') {
    console.error('Dialog component not loaded! Please include dialog.js before this script.');
}

/**
 * 替换页面中所有使用原生confirm的表单提交
 * 使用美观的自定义对话框替代
 */
document.addEventListener('DOMContentLoaded', function() {
    // 查找所有带有onsubmit="return confirm(...)"的表单
    const forms = document.querySelectorAll('form[onsubmit*="return confirm"]');
    
    forms.forEach(form => {
        // 移除原有的onsubmit属性
        const originalOnSubmit = form.getAttribute('onsubmit');
        form.removeAttribute('onsubmit');
        
        // 提取确认消息
        let confirmMessage = '';
        if (originalOnSubmit) {
            const match = originalOnSubmit.match(/confirm\(['"](.*?)['"]\)/);
            if (match && match[1]) {
                confirmMessage = match[1];
            }
        }
        
        // 为表单添加提交事件监听器
        form.addEventListener('submit', function(event) {
            // 阻止表单默认提交
            event.preventDefault();
            
            // 根据表单中的action值确定按钮文本和样式
            const action = form.querySelector('input[name="action"]')?.value;
            let confirmText = '确定';
            let cancelText = '取消';
            
            // 根据不同操作设置不同的按钮文本
            if (action === 'toggle_status') {
                const isActive = form.querySelector('button').textContent.trim().includes('禁用');
                confirmText = isActive ? '禁用' : '启用';
            } else if (action === 'set_admin') {
                confirmText = '设为管理员';
            } else if (action === 'set_user') {
                confirmText = '设为用户';
            }
            
            // 显示自定义对话框
            dialog.confirm(
                confirmMessage,
                () => {
                    // 用户点击确认，提交表单
                    form.submit();
                },
                () => {
                    // 用户点击取消，不做任何操作
                },
                confirmText,
                cancelText
            );
        });
    });
});