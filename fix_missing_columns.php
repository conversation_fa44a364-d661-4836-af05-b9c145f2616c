<?php
// 包含配置文件
require_once 'config.php';

try {
    // 获取数据库连接
    $pdo = getDbConnection();
    
    // 检查is_top列是否存在
    $stmt = $pdo->prepare("SHOW COLUMNS FROM products LIKE 'is_top'");
    $stmt->execute();
    $is_top_exists = $stmt->rowCount() > 0;
    
    // 检查last_top_time列是否存在
    $stmt = $pdo->prepare("SHOW COLUMNS FROM products LIKE 'last_top_time'");
    $stmt->execute();
    $last_top_time_exists = $stmt->rowCount() > 0;
    
    // 添加is_top列（如果不存在）
    if (!$is_top_exists) {
        $sql = "ALTER TABLE `products` ADD COLUMN `is_top` TINYINT(1) NOT NULL DEFAULT 0 AFTER `is_active`";
        $pdo->exec($sql);
        echo "<p>已添加is_top列到products表</p>";
    } else {
        echo "<p>is_top列已存在</p>";
    }
    
    // 添加last_top_time列（如果不存在）
    if (!$last_top_time_exists) {
        $sql = "ALTER TABLE products ADD COLUMN last_top_time TIMESTAMP NULL DEFAULT NULL";
        $pdo->exec($sql);
        echo "<p>已添加last_top_time列到products表</p>";
        
        // 更新现有置顶商品的last_top_time
        $sql = "UPDATE products SET last_top_time = CURRENT_TIMESTAMP WHERE is_top = 1";
        $pdo->exec($sql);
        echo "<p>已更新现有置顶商品的last_top_time</p>";
    } else {
        echo "<p>last_top_time列已存在</p>";
    }
    
    echo "<p>数据库修复完成！</p>";
    echo "<p><a href='/index.php'>返回首页</a></p>";
    
} catch (PDOException $e) {
    echo "<p>修复数据库失败: " . $e->getMessage() . "</p>";
}