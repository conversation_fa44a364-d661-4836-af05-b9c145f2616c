<?php
require_once 'config.php';

// 页面标题
$pageTitle = '数据库导出';
$exportStatus = 'processing'; // 添加导出状态变量
$errorMessage = ''; // 添加错误信息变量
$successMessage = ''; // 添加成功信息变量

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('login.php');
}

// 检查是否是AJAX请求
$isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

try {
    $pdo = getDbConnection();
    
    // 设置导出文件名
    $timestamp = date('Y-m-d_H-i-s');
    $filename = "database_backup_{$timestamp}.sql";
    $backup_dir = __DIR__ . '/数据库';

    // 确保备份目录存在
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
    }

    $backup_file = $backup_dir . '/' . $filename;
    $output = '';
    
    // 获取数据库名称
    $dbName = DB_NAME;
    
    // 添加创建数据库和使用数据库语句
    $output .= "-- 创建数据库\n";
    $output .= "CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
    $output .= "USE `{$dbName}`;\n\n";

    // 获取所有视图
    $views = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE LIKE 'VIEW'")->fetchAll(PDO::FETCH_COLUMN);
    
    // 删除所有视图
    $output .= "\n-- 删除所有视图\n";
    foreach ($views as $view) {
        $output .= "DROP VIEW IF EXISTS `{$view}`;\n";
    }
    
    // 删除所有存储过程
    $output .= "\n-- 删除所有存储过程\n";
    $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($procedures as $procedure) {
        $output .= "DROP PROCEDURE IF EXISTS `{$procedure}`;\n";
    }
    
    // 删除所有函数
    $output .= "\n-- 删除所有函数\n";
    $functions = $pdo->query("SHOW FUNCTION STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($functions as $function) {
        $output .= "DROP FUNCTION IF EXISTS `{$function}`;\n";
    }
    
    // 删除所有事件
    $output .= "\n-- 删除所有事件\n";
    $events = $pdo->query("SHOW EVENTS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($events as $event) {
        $output .= "DROP EVENT IF EXISTS `{$event}`;\n";
    }
    
    // 删除所有触发器
    $output .= "\n-- 删除所有触发器\n";
    $triggers = $pdo->query("SHOW TRIGGERS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($triggers as $trigger) {
        $output .= "DROP TRIGGER IF EXISTS `{$trigger}`;\n";
    }
    
    // 定义表的创建顺序，确保外键约束不会出错
    $tables = array(
        'users',      // 基础表，无外键依赖
        'categories', // 基础表，无外键依赖
        'products',   // 依赖于categories表（外键约束）
        'orders',     // 依赖于users表
        'order_items', // 依赖于orders和products表
        'cart_items'  // 依赖于users和products表
    );
    
    // 验证所有表是否存在
    $existingTables = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE = 'BASE TABLE'")->fetchAll(PDO::FETCH_COLUMN);
    $tables = array_intersect($tables, $existingTables);
    
    foreach ($tables as $table) {
        // 添加删除表和创建表语句
        $output .= "\n-- 导出 {$table} 表结构\n";
        $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
        
        $create_table = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch(PDO::FETCH_ASSOC);
        $output .= $create_table['Create Table'] . ";\n\n";

        // 获取表数据
        $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $output .= "-- 导出 {$table} 表数据\n";
            $columns = array_keys($rows[0]);
            $output .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = $pdo->quote($value);
                    }
                }
                $values[] = "(" . implode(", ", $rowValues) . ")";
            }
            $output .= implode(",\n", $values) . ";\n";
        }
        
        $output .= "\n";
    }

    // 写入文件
    if (file_put_contents($backup_file, $output)) {
        // 设置导出状态为成功
        $exportStatus = 'success';
        $successMessage = '数据库导出成功！文件已保存在：' . $filename;
        
        // 设置成功消息到会话中，以便在用户管理页面显示
        setMessage('success', $successMessage);
        
        // 如果是AJAX请求，返回JSON响应
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'success', 'message' => $successMessage]);
            exit;
        }
        
        // 否则直接重定向到用户管理页面
        redirect('admin/users.php');
    } else {
        // 设置导出状态为失败
        $exportStatus = 'error';
        $errorMessage = '写入备份文件失败，请检查目录权限';
        
        // 设置错误消息到会话中
        setMessage('danger', $errorMessage);
        
        // 如果是AJAX请求，返回JSON响应
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => $errorMessage]);
            exit;
        }
        
        // 否则直接重定向到用户管理页面
        redirect('admin/users.php');
    }
} catch (Exception $e) {
    // 设置导出状态为失败
    $exportStatus = 'error';
    $errorMessage = '导出失败：' . $e->getMessage();
    
    // 设置错误消息到会话中
    setMessage('danger', $errorMessage);
    
    // 如果是AJAX请求，返回JSON响应
    if ($isAjax) {
        header('Content-Type: application/json');
        echo json_encode(['status' => 'error', 'message' => $errorMessage]);
        exit;
    }
    
    // 否则直接重定向到用户管理页面
    redirect('admin/users.php');
}

// 如果代码执行到这里，说明不是AJAX请求，且没有重定向
// 为了兼容性，保留原来的HTML输出，但正常情况下不会执行到这里
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="notification.css">
    <script>
        // 页面加载完成后自动重定向到用户管理页面
        window.onload = function() {
            window.location.href = 'admin/users.php';
        };
    </script>
</head>
<body>
    <div class="container mt-4">
        <h1><?php echo $pageTitle; ?></h1>
        <div id="processing-container">
            <p>正在处理，请稍候...</p>
            <div class="d-flex justify-content-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>