<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取搜索参数
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$perPage = 10;

// 处理订单状态更新
if (isset($_POST['action']) && isset($_POST['order_id'])) {
    $orderId = (int)$_POST['order_id'];
    $action = $_POST['action'];
    $newStatus = '';
    
    try {
        if ($action === 'update_status' && isset($_POST['status'])) {
            $newStatus = $_POST['status'];
            $validStatuses = ['pending', 'paid', 'shipped', 'completed', 'cancelled'];
            
            if (in_array($newStatus, $validStatuses)) {
                $stmt = $pdo->prepare("UPDATE orders SET status = :status, updated_at = NOW() WHERE id = :id");
                $stmt->execute([
                    'status' => $newStatus,
                    'id' => $orderId
                ]);
                setMessage('success', '订单状态已更新为：' . getStatusName($newStatus));
            } else {
                setMessage('danger', '无效的订单状态');
            }
        }
    } catch (PDOException $e) {
        setMessage('danger', '操作失败：' . $e->getMessage());
    }
    
    // 根据更新后的状态重定向到相应的订单列表
    if ($action === 'update_status' && isset($_POST['status'])) {
        $redirectStatus = $_POST['status'];
        $params = ['status=' . urlencode($redirectStatus)];
        
        if ($search) {
            $params[] = 'search=' . urlencode($search);
        }
        
        // 修改重定向逻辑：
        // 1. 对于已发货、已完成和已取消状态，直接跳转到对应标签页的第一页
        // 2. 对于取消订单或恢复订单，保持在当前页码
        if (($redirectStatus === 'cancelled' && $status === 'cancelled') || 
            ($redirectStatus === 'pending' && $status === 'cancelled') || 
            ($redirectStatus === 'shipped' && $status === 'paid') || 
            ($redirectStatus === 'completed' && $status === 'shipped')) {
            // 在同一标签页内操作或者是常规的状态流转（已支付->已发货->已完成），保持当前页码
            if ($page > 1) {
                $params[] = 'page=' . $page;
            }
        }
        
        $redirectUrl = 'orders.php?' . implode('&', $params);
        redirect($redirectUrl);
    } else {
        $redirectUrl = 'orders.php';
        $params = [];
        if ($search) {
            $params[] = 'search=' . urlencode($search);
        }
        if ($status) {
            $params[] = 'status=' . urlencode($status);
        }
        if ($page > 1) {
            $params[] = 'page=' . $page;
        }
        if (!empty($params)) {
            $redirectUrl .= '?' . implode('&', $params);
        }
        redirect($redirectUrl);
    }
}

// 构建订单查询
$sql = "SELECT SQL_CALC_FOUND_ROWS o.*, u.username 
        FROM orders o 
        JOIN users u ON o.user_id = u.id 
        WHERE 1";
$params = [];

if ($search) {
    $sql .= " AND (o.id LIKE :search OR u.username LIKE :search OR u.email LIKE :search)";
    $params['search'] = "%$search%";
}

if ($status) {
    $sql .= " AND o.status = :status";
    $params['status'] = $status;
}

$sql .= " ORDER BY o.created_at DESC LIMIT :offset, :limit";
$offset = ($page - 1) * $perPage;

// 执行查询
$stmt = $pdo->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
$stmt->execute();
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取总记录数
$stmt = $pdo->query("SELECT FOUND_ROWS()");
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 获取消息提示
$messages = getMessages();

// 订单状态名称映射
function getStatusName($status) {
    $statusNames = [
        'pending' => '待支付',
        'paid' => '已支付',
        'shipped' => '已发货',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statusNames[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin-search.css">
    <link rel="stylesheet" href="../dialog.css">
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container mb-4">
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>订单管理</h1>
            <form class="admin-search-form" method="get">
                <input type="search" name="search" class="admin-search-input" placeholder="搜索订单号/用户名" value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="admin-search-button">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </form>
        </div>

        <div class="mb-4">
            <div class="btn-group">
                <a href="orders.php" class="btn btn-outline-primary <?php echo $status === '' ? 'active' : ''; ?>">
                    全部订单
                </a>
                <a href="orders.php?status=pending<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline-warning <?php echo $status === 'pending' ? 'active' : ''; ?>">
                    待支付
                </a>
                <a href="orders.php?status=paid<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline-info <?php echo $status === 'paid' ? 'active' : ''; ?>">
                    已支付
                </a>
                <a href="orders.php?status=shipped<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline-primary <?php echo $status === 'shipped' ? 'active' : ''; ?>">
                    已发货
                </a>
                <a href="orders.php?status=completed<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline-success <?php echo $status === 'completed' ? 'active' : ''; ?>">
                    已完成
                </a>
                <a href="orders.php?status=cancelled<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline-danger <?php echo $status === 'cancelled' ? 'active' : ''; ?>">
                    已取消
                </a>
            </div>
            <div class="mt-2 small text-muted">
                <p>订单状态说明：</p>
                <ul>
                    <li><strong>已发货</strong>：当管理员将"已支付"的订单标记为"已发货"后，订单会显示在此标签页</li>
                    <li><strong>已完成</strong>：当管理员将"已发货"的订单标记为"已完成"后，订单会显示在此标签页</li>
                    <li><strong>已取消</strong>：当管理员取消订单，或订单支付超时自动取消后，订单会显示在此标签页</li>
                </ul>
            </div>
        </div>

        <div class="table-responsive" style="min-height: 600px;">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th style="min-width: 80px;">订单号</th>
                        <th style="min-width: 100px;">用户</th>
                        <th style="min-width: 100px;">金额</th>
                        <th style="min-width: 100px;">状态</th>
                        <th style="min-width: 160px;">下单时间</th>
                        <th style="min-width: 150px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><?php echo $order['id']; ?></td>
                            <td><?php echo htmlspecialchars($order['username']); ?></td>
                            <td class="text-success fw-bold">¥<?php echo number_format($order['total_amount'], 2); ?></td>
                            <td>
                                <?php 
                                $statusClass = [
                                    'pending' => 'warning',
                                    'paid' => 'info',
                                    'shipped' => 'primary',
                                    'completed' => 'success',
                                    'cancelled' => 'danger'
                                ];
                                $badgeClass = $statusClass[$order['status']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?php echo $badgeClass; ?>">
                                    <?php echo getStatusName($order['status']); ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        操作
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-lg-end">
                                        <li>
                                            <a href="order_detail.php?id=<?php echo $order['id']; ?>" class="dropdown-item">
                                                <i class="bi bi-eye"></i> 查看详情
                                            </a>
                                        </li>
                                        <?php if ($order['status'] === 'pending' || $order['status'] === 'paid'): ?>
                                            <li>
                                                <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要取消该订单吗？', () => this.submit());">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="status" value="cancelled">
                                                    <button type="submit" class="dropdown-item text-danger">
                                                        <i class="bi bi-x-circle"></i> 取消订单
                                                    </button>
                                                </form>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'cancelled'): ?>
                                            <li>
                                                <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要恢复该订单吗？', () => this.submit());">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="status" value="pending">
                                                    <button type="submit" class="dropdown-item text-success">
                                                        <i class="bi bi-arrow-counterclockwise"></i> 恢复订单
                                                    </button>
                                                </form>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'paid'): ?>
                                            <li>
                                                <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要将订单标记为已发货吗？', () => this.submit());">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="status" value="shipped">
                                                    <button type="submit" class="dropdown-item text-primary">
                                                        <i class="bi bi-truck"></i> 标记为已发货
                                                    </button>
                                                </form>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'shipped'): ?>
                                            <li>
                                                <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要将订单标记为已完成吗？', () => this.submit());">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="status" value="completed">
                                                    <button type="submit" class="dropdown-item text-success">
                                                        <i class="bi bi-check-circle"></i> 标记为已完成
                                                    </button>
                                                </form>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    <?php if (empty($orders)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="bi bi-inbox text-muted"></i> 暂无订单数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $status ? '&status=' . urlencode($status) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../dialog.js"></script>
</body>
</html>