<?php
require_once 'includes/config.php';

// 添加商品状态字段
$sql = "SHOW COLUMNS FROM products LIKE 'status'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    $sql = "ALTER TABLE products ADD COLUMN status TINYINT(1) NOT NULL DEFAULT 1 AFTER stock";
    if ($conn->query($sql) === TRUE) {
        echo "成功添加商品状态字段！";
    } else {
        echo "添加字段时出错: " . $conn->error;
    }
} else {
    echo "状态字段已存在，无需添加。";
}
?>