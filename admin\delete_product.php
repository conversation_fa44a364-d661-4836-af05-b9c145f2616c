<?php
require_once '../config.php';

// 设置响应头为 JSON
header('Content-Type: application/json');

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => '无权限执行此操作']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$productId = isset($input['product_id']) ? (int)$input['product_id'] : 0;

if ($productId <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的商品ID']);
    exit;
}

try {
    $pdo = getDbConnection();
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 先删除购物车中的相关数据
    $stmt = $pdo->prepare("DELETE FROM cart_items WHERE product_id = :product_id");
    $stmt->execute(['product_id' => $productId]);
    
    // 再删除订单项中的相关数据
    $stmt = $pdo->prepare("DELETE FROM order_items WHERE product_id = :product_id");
    $stmt->execute(['product_id' => $productId]);
    
    // 最后删除商品
    $stmt = $pdo->prepare("DELETE FROM products WHERE id = :product_id");
    $stmt->execute(['product_id' => $productId]);
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    // 如果发生错误，回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>