// 商品管理页面的JavaScript功能
$(document).ready(function() {
    // 自动隐藏提示栏
    $('.alert').each(function() {
        var alert = $(this);
        setTimeout(function() {
            alert.fadeOut('slow', function() {
                $(this).remove();
            });
        }, 3000); // 3秒后自动隐藏
    });

    // 处理删除商品按钮
    $('.btn-danger').on('click', function(e) {
        return true;
    });

    // 处理商品上架按钮
    $('.btn-success').on('click', function(e) {
        // 确保表单中包含分类ID，防止创建重复分类
        var categoryId = $(this).data('category-id');
        if (categoryId) {
            // 如果是编辑分类操作，确保表单中包含分类ID
            var form = $(this).closest('form');
            if (form.find('input[name="id"]').length === 0) {
                form.append('<input type="hidden" name="id" value="' + categoryId + '">');
            }
        }
        return true;
    });

    // 处理商品下架按钮
    $('.btn-warning').on('click', function(e) {
        return true;
    });
});