<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>127.0.0.10 提示框示例</title>
    <link rel="stylesheet" href="dialog.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .demo-container {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            color: #e60000;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #ff4b4b, #e60000);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(230, 0, 0, 0.2);
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(230, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>对话框示例</h1>
        <p>点击下面的按钮显示自定义对话框</p>
        <button id="showDialog" class="btn">显示删除确认框</button>
    </div>

    <script src="dialog.js"></script>
    <script>
        document.getElementById('showDialog').addEventListener('click', function() {
            dialog.confirm(
                '确定要删除该商品吗？此操作不可恢复！',
                function() { alert('您点击了确定按钮'); },
                function() { alert('您点击了取消按钮'); },
                '确定',
                '取消'
            );
        });
    </script>
</body>
</html>