<?php
// 测试数据库连接和函数
require_once 'config.php';

echo "<h2>数据库连接测试</h2>";

try {
    // 测试数据库连接
    $pdo = getDbConnection();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 测试表是否存在
    $tables = ['users', 'categories', 'products'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ 表 '$table' 存在</p>";
            
            // 显示表中的记录数
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;记录数: $count</p>";
        } else {
            echo "<p>❌ 表 '$table' 不存在</p>";
        }
    }
    
    // 测试函数
    echo "<h3>函数测试</h3>";
    
    if (function_exists('getAllCategories')) {
        $categories = getAllCategories();
        echo "<p>✅ getAllCategories() 函数正常，返回 " . count($categories) . " 个分类</p>";
        if (!empty($categories)) {
            echo "<ul>";
            foreach ($categories as $cat) {
                echo "<li>{$cat['name']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ getAllCategories() 函数不存在</p>";
    }
    
    if (function_exists('getAllProducts')) {
        $products = getAllProducts();
        echo "<p>✅ getAllProducts() 函数正常，返回 " . count($products) . " 个产品</p>";
        if (!empty($products)) {
            echo "<ul>";
            foreach ($products as $product) {
                echo "<li>{$product['name']} - {$product['category_name']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ getAllProducts() 函数不存在</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<p><a href='index.php'>访问网站首页</a></p>";
echo "<p><a href='install/quick_install.php'>重新安装</a></p>";
?>
