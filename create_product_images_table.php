<?php
// 数据库连接信息
require_once 'config.php';

try {
    // 获取数据库连接
    $pdo = getDbConnection();
    
    // 读取SQL文件内容
    $sql = file_get_contents(__DIR__ . '/数据库/create_product_images_table.sql');
    
    // 执行SQL语句
    $result = $pdo->exec($sql);
    
    echo "<p>商品图片表创建成功！</p>";
    echo "<p><a href='/admin/add_product.php'>返回添加商品页面</a></p>";
    
} catch (PDOException $e) {
    echo "<p>创建表失败: " . $e->getMessage() . "</p>";
}
?>