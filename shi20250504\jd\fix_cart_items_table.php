<?php
// 此脚本用于修复cart_items表的结构和外键约束
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    echo "<h2>购物车表(cart_items)修复工具</h2>";
    
    // 检查cart_items表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'cart_items'")->fetchAll();
    if (count($stmt) === 0) {
        echo "<p>cart_items表不存在，正在创建...</p>";
        
        // 创建cart_items表
        $sql = "CREATE TABLE IF NOT EXISTS `cart_items` (
            `id` INT PRIMARY KEY AUTO_INCREMENT,
            `user_id` INT NOT NULL,
            `product_id` INT NOT NULL,
            `quantity` INT NOT NULL DEFAULT 1,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            KEY `user_id` (`user_id`),
            KEY `product_id` (`product_id`),
            CONSTRAINT `cart_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
            CONSTRAINT `cart_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products`(`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $pdo->exec($sql);
        
        echo "<p>cart_items表创建成功！</p>";
    } else {
        echo "<p>cart_items表已存在，检查表结构...</p>";
        
        // 检查表结构
        $stmt = $pdo->query("DESCRIBE cart_items");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // 检查必要的字段
        $requiredColumns = ['id', 'user_id', 'product_id', 'quantity', 'created_at'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (!empty($missingColumns)) {
            echo "<p>发现缺少以下字段: " . implode(", ", $missingColumns) . "</p>";
            
            // 添加缺失的字段
            foreach ($missingColumns as $column) {
                switch ($column) {
                    case 'id':
                        $pdo->exec("ALTER TABLE cart_items ADD COLUMN id INT PRIMARY KEY AUTO_INCREMENT FIRST");
                        break;
                    case 'user_id':
                        $pdo->exec("ALTER TABLE cart_items ADD COLUMN user_id INT NOT NULL AFTER id");
                        break;
                    case 'product_id':
                        $pdo->exec("ALTER TABLE cart_items ADD COLUMN product_id INT NOT NULL AFTER user_id");
                        break;
                    case 'quantity':
                        $pdo->exec("ALTER TABLE cart_items ADD COLUMN quantity INT NOT NULL DEFAULT 1 AFTER product_id");
                        break;
                    case 'created_at':
                        $pdo->exec("ALTER TABLE cart_items ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER quantity");
                        break;
                }
            }
            
            echo "<p>已添加缺失的字段</p>";
        } else {
            echo "<p>表字段结构正常</p>";
        }
        
        // 检查外键约束
        $stmt = $pdo->query("SELECT * FROM information_schema.KEY_COLUMN_USAGE 
                            WHERE TABLE_SCHEMA = '" . DB_NAME . "' 
                            AND TABLE_NAME = 'cart_items' 
                            AND REFERENCED_TABLE_NAME IS NOT NULL");
        $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasUserFk = false;
        $hasProductFk = false;
        
        foreach ($constraints as $constraint) {
            if ($constraint['COLUMN_NAME'] == 'user_id' && $constraint['REFERENCED_TABLE_NAME'] == 'users') {
                $hasUserFk = true;
            }
            if ($constraint['COLUMN_NAME'] == 'product_id' && $constraint['REFERENCED_TABLE_NAME'] == 'products') {
                $hasProductFk = true;
            }
        }
        
        // 添加缺失的外键约束
        if (!$hasUserFk) {
            echo "<p>缺少user_id外键约束，正在添加...</p>";
            try {
                // 先添加索引
                $pdo->exec("ALTER TABLE cart_items ADD INDEX user_id (user_id)");
                // 再添加外键约束
                $pdo->exec("ALTER TABLE cart_items ADD CONSTRAINT cart_items_ibfk_1 FOREIGN KEY (user_id) REFERENCES users(id)");
                echo "<p>user_id外键约束添加成功</p>";
            } catch (PDOException $e) {
                echo "<p>添加user_id外键约束失败: " . $e->getMessage() . "</p>";
            }
        }
        
        if (!$hasProductFk) {
            echo "<p>缺少product_id外键约束，正在添加...</p>";
            try {
                // 先添加索引
                $pdo->exec("ALTER TABLE cart_items ADD INDEX product_id (product_id)");
                // 再添加外键约束
                $pdo->exec("ALTER TABLE cart_items ADD CONSTRAINT cart_items_ibfk_2 FOREIGN KEY (product_id) REFERENCES products(id)");
                echo "<p>product_id外键约束添加成功</p>";
            } catch (PDOException $e) {
                echo "<p>添加product_id外键约束失败: " . $e->getMessage() . "</p>";
            }
        }
        
        if ($hasUserFk && $hasProductFk) {
            echo "<p>外键约束检查完成，一切正常</p>";
        }
    }
    
    echo "<p>购物车表修复完成！</p>";
    echo "<p><a href='index.php' class='btn btn-primary'>返回首页</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>数据库操作错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p><a href='fix_database.php' class='btn btn-primary'>运行数据库修复工具</a></p>";
}
?>