<?php
require_once 'config.php';

// 获取商品ID
$productId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// 获取商品详情
function getProductDetails($productId) {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          JOIN categories c ON p.category_id = c.id 
                          WHERE p.id = :id");
    $stmt->execute(['id' => $productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        // 获取商品的所有图片
        $stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = :product_id ORDER BY is_primary DESC");
        $stmt->execute(['product_id' => $productId]);
        $product['images'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    return $product;
}

// 添加到购物车
if (isset($_POST['add_to_cart']) && isLoggedIn()) {
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
    $userId = getCurrentUserId();
    
    try {
        $pdo = getDbConnection();
        
        // 检查库存
        $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = :id");
        $stmt->execute(['id' => $productId]);
        $stock = $stmt->fetchColumn();
        
        if ($stock >= $quantity) {
            // 检查购物车是否已有该商品
            $stmt = $pdo->prepare("SELECT id, quantity FROM cart_items 
                                  WHERE user_id = :user_id AND product_id = :product_id");
            $stmt->execute([
                'user_id' => $userId,
                'product_id' => $productId
            ]);
            $cartItem = $stmt->fetch();
            
            if ($cartItem) {
                // 更新数量
                $stmt = $pdo->prepare("UPDATE cart_items SET quantity = quantity + :quantity 
                                      WHERE id = :id");
                $stmt->execute([
                    'quantity' => $quantity,
                    'id' => $cartItem['id']
                ]);
            } else {
                // 新增购物车项
                $stmt = $pdo->prepare("INSERT INTO cart_items (user_id, product_id, quantity) 
                                      VALUES (:user_id, :product_id, :quantity)");
                $stmt->execute([
                    'user_id' => $userId,
                    'product_id' => $productId,
                    'quantity' => $quantity
                ]);
            }
            
            setMessage('success', '商品已成功添加到购物车');
        } else {
            setMessage('danger', '商品库存不足');
        }
    } catch (PDOException $e) {
        setMessage('danger', '添加到购物车失败，请稍后重试');
    }
    
    redirect('product.php?id=' . $productId);
}

// 处理立即购买
if (isset($_POST['buy_now']) && isLoggedIn()) {
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
    $userId = getCurrentUserId();
    
    try {
        $pdo = getDbConnection();
        
        // 检查库存
        $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = :id");
        $stmt->execute(['id' => $productId]);
        $stock = $stmt->fetchColumn();
        
        if ($stock >= $quantity) {
            // 清空用户购物车
            $stmt = $pdo->prepare("DELETE FROM cart_items WHERE user_id = :user_id");
            $stmt->execute(['user_id' => $userId]);
            
            // 添加商品到购物车
            $stmt = $pdo->prepare("INSERT INTO cart_items (user_id, product_id, quantity) 
                                  VALUES (:user_id, :product_id, :quantity)");
            $stmt->execute([
                'user_id' => $userId,
                'product_id' => $productId,
                'quantity' => $quantity
            ]);
            
            // 重定向到结算页面
            redirect('checkout.php');
        } else {
            setMessage('danger', '商品库存不足');
            redirect('product.php?id=' . $productId);
        }
    } catch (PDOException $e) {
        setMessage('danger', '处理订单失败，请稍后重试');
        redirect('product.php?id=' . $productId);
    }
}

// 获取商品信息
$product = getProductDetails($productId);
if (!$product) {
    setMessage('danger', '商品不存在');
    redirect('index.php');
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($product['name']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="dialog.css">
    <style>
        .product-image {
            max-width: 100%;
            height: auto;
        }
        .quantity-input {
            width: 100px;
        }
        .message-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
            max-width: 350px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 15px 20px;
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }
        .message-popup.show {
            opacity: 1;
        }
        .message-popup.success {
            border-left: 4px solid #28a745;
        }
        .message-popup.danger {
            border-left: 4px solid #dc3545;
        }
        /* 新增面包屑导航样式 */
        .breadcrumb {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 12px 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .breadcrumb-item a {
            color: #4f46e5;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .breadcrumb-item a:hover {
            color: #4338ca;
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: #6b7280;
            font-weight: 600;
        }
        .breadcrumb-item + .breadcrumb-item::before {
            content: "/";
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <!-- 消息提示将通过JavaScript动态显示 -->

        <!-- 商品详情 -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <?php if (!empty($product['images'])): ?>
                <!-- 图片轮播 -->
                <div id="productImageCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <?php $first = true; foreach ($product['images'] as $index => $image): ?>
                        <div class="carousel-item <?php echo $first ? 'active' : ''; ?>">
                            <img src="<?php echo htmlspecialchars($image['image_url']); ?>" class="d-block w-100 product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        </div>
                        <?php $first = false; endforeach; ?>
                    </div>
                    <?php if (count($product['images']) > 1): ?>
                    <button class="carousel-control-prev" type="button" data-bs-target="#productImageCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">上一张</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#productImageCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">下一张</span>
                    </button>
                    <?php endif; ?>
                </div>
                
                <!-- 缩略图导航 -->
                <?php if (count($product['images']) > 1): ?>
                <div class="row mt-2">
                    <?php foreach ($product['images'] as $index => $image): ?>
                    <div class="col-3 mb-2">
                        <img src="<?php echo htmlspecialchars($image['image_url']); ?>" 
                             class="img-thumbnail" alt="缩略图" 
                             style="cursor: pointer;" 
                             onclick="$('#productImageCarousel').carousel(<?php echo $index; ?>)">
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <img src="<?php echo !empty($product['image_url']) ? htmlspecialchars($product['image_url']) : 'uploads/default-product.jpg'; ?>" 
                     class="product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                        <li class="breadcrumb-item"><a href="index.php?category=<?php echo $product['category_id']; ?>">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($product['name']); ?></li>
                    </ol>
                </nav>
                <h1 class="h2 mb-3"><?php echo htmlspecialchars($product['name']); ?></h1>
                <p class="h3 text-danger mb-3">¥<?php echo number_format($product['price'], 2); ?></p>
                <p class="mb-3"><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                <p class="mb-3">库存：<?php echo $product['stock']; ?> 件</p>
                
                <?php if (isLoggedIn()): ?>
                    <form method="post" class="mb-3">
                        <div class="mb-3">
                            <label for="quantity" class="form-label">数量：</label>
                            <input type="number" class="form-control quantity-input" id="quantity" name="quantity" 
                                   value="1" min="1" max="<?php echo $product['stock']; ?>">
                        </div>
                        <link rel="stylesheet" href="product-buttons.css">
                        <div class="product-action-buttons">
                            <button type="submit" name="add_to_cart" class="btn btn-primary btn-lg" 
                                    <?php echo $product['stock'] > 0 ? '' : 'disabled'; ?>>
                                <?php echo $product['stock'] > 0 ? '加入购物车' : '暂时缺货'; ?>
                            </button>
                            <button type="submit" name="buy_now" class="btn btn-danger btn-lg" 
                                    <?php echo $product['stock'] > 0 ? '' : 'disabled'; ?>>
                                <?php echo $product['stock'] > 0 ? '立即购买' : '暂时缺货'; ?>
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-info">请<a href="login.php">登录</a>后购买商品</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="dialog.js"></script>
    <script>
    // 显示消息弹出框
    function showMessagePopup(type, text) {
        const popup = document.createElement('div');
        popup.className = `message-popup ${type}`;
        popup.innerHTML = text;
        document.body.appendChild(popup);

        // 触发重排后显示动画
        setTimeout(() => popup.classList.add('show'), 10);

        // 3秒后自动消失
        setTimeout(() => {
            popup.classList.remove('show');
            setTimeout(() => popup.remove(), 300);
        }, 3000);
    }

    // 处理PHP传来的消息
    <?php foreach ($messages as $message): ?>
        showMessagePopup('<?php echo $message['type']; ?>', '<?php echo addslashes($message['text']); ?>');
    <?php endforeach; ?>
    </script>
</body>
</html>