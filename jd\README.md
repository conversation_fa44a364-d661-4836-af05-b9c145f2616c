# 京东商城网站安装说明

## 环境要求

- PHP 8.2 或更高版本
- MySQL 8.0 或更高版本
- Web服务器（Apache/Nginx/PHP内置服务器）
- PHP PDO扩展（用于数据库连接）
- PHP GD扩展（用于图片处理）

## 安装步骤

### 1. 部署网站文件

1. 将网站所有文件上传到Web服务器的根目录
2. 确保Web服务器对`uploads`目录具有写入权限

### 2. 配置数据库

1. 创建新的MySQL数据库
2. 编辑`config.php`文件，修改数据库连接信息：

```php
// 数据库配置
define('DB_HOST', 'localhost');     // 数据库服务器地址
define('DB_NAME', 'your_db_name');  // 数据库名称
define('DB_USER', 'your_username'); // 数据库用户名
define('DB_PASS', 'your_password'); // 数据库密码

// 网站配置
define('SITE_NAME', '京东商城');    // 网站名称
```

### 3. 初始化数据库

1. 访问 `http://你的域名/init_db.php` 初始化数据库表结构
2. 初始化完成后，系统会自动创建一个管理员账号：
   - 用户名：admin
   - 密码：admin123

### 4. 测试安装

1. 访问网站首页 `http://你的域名/`
2. 使用管理员账号登录：
   - 访问 `http://你的域名/login.php`
   - 输入管理员账号和密码
3. 登录后访问管理后台 `http://你的域名/admin/`

## 安装后配置

1. 立即修改默认管理员密码：
   - 登录管理后台
   - 点击右上角的"修改密码"链接
   - 设置新的安全密码

2. 配置商品分类：
   - 在管理后台点击"分类管理"
   - 添加商品分类

3. 上传商品：
   - 在管理后台点击"商品管理"
   - 点击"添加商品"按钮
   - 填写商品信息并上传商品图片

## 安全建议

1. 定期备份数据库（可使用管理后台的"导出数据库备份"功能）
2. 确保`config.php`文件的权限设置正确，防止未经授权的访问
3. 定期更新PHP和MySQL版本以修复安全漏洞
4. 使用强密码并定期更改

## 常见问题

1. 图片上传失败
   - 检查`uploads`目录权限
   - 确保PHP GD扩展已启用

2. 数据库连接错误
   - 验证数据库连接参数是否正确
   - 确保MySQL服务正在运行

3. 页面显示空白
   - 检查PHP错误日志
   - 确保PHP版本满足要求

## 技术支持

如果您在安装过程中遇到任何问题，请检查以上说明和常见问题解答。如果问题仍然存在，请联系技术支持。

## 许可说明

本系统仅供学习和演示使用，请勿用于商业目的。