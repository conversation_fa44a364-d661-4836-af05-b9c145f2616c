<?php
// 检查是否已经安装
if (file_exists('config.php')) {
    $config = file_get_contents('config.php');
    if (strpos($config, 'DB_INSTALLED') !== false && !isset($_GET['force_install'])) {
        // 尝试测试数据库连接
        require_once 'config.php';
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查必要的表是否存在
            $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
                if (count($stmt) === 0) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                // 如果缺少必要的表，但配置文件存在，提示用户修复
                $needFix = true;
            } else {
                // 如果连接成功且没有强制安装参数，则重定向到首页
                header('Location: index.php');
                exit;
            }
        } catch (PDOException $e) {
            // 数据库连接失败，继续安装流程
            // 不做任何操作，继续执行安装向导
        }
    }
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$dbHost = $_POST['db_host'] ?? 'localhost';
$dbUser = $_POST['db_user'] ?? '';
$dbPass = $_POST['db_pass'] ?? '';
$dbName = $_POST['db_name'] ?? '';
$siteName = $_POST['site_name'] ?? '软件商城';
$siteUrl = $_POST['site_url'] ?? 'http://127.0.0.1:8000';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 2) {
        if (empty($dbUser) || empty($dbName)) {
            $error = '请填写所有必填字段';
        } else {
            try {
                // 尝试连接数据库
                $pdo = new PDO("mysql:host={$dbHost}", $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 检查数据库是否已存在
                $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbName}'");
                $dbExists = $stmt->rowCount() > 0;
                
                if ($dbExists) {
                    // 尝试使用已存在的数据库
                    $pdo->exec("USE `{$dbName}`);
                    
                    // 检查是否有表存在
                    $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
                    $existingTables = [];
                    
                    foreach ($tables as $table) {
                        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
                        if (count($stmt) > 0) {
                            $existingTables[] = $table;
                        }
                    }
                    
                    if (!empty($existingTables)) {
                        $error = '数据库已存在且包含表: ' . implode(', ', $existingTables) . '。请使用其他数据库名称或清空此数据库。';
                    } else {
                        // 数据库存在但没有表，可以继续使用
                        $dbReady = true;
                    }
                } else {
                    // 创建新数据库
                    $pdo->exec("CREATE DATABASE `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `{$dbName}`);
                    $dbReady = true;
                }
                
                if (isset($dbReady) && $dbReady) {
                    // 创建配置文件内容
                    $configContent = "<?php\n";
                    $configContent .= "// 数据库连接配置\n";
                    $configContent .= "define('DB_HOST', '{$dbHost}');\n";
                    $configContent .= "define('DB_USER', '{$dbUser}');\n";
                    $configContent .= "define('DB_PASS', '{$dbPass}');\n";
                    $configContent .= "define('DB_NAME', '{$dbName}');\n\n";
                    $configContent .= "// 网站配置\n";
                    $configContent .= "define('SITE_NAME', '{$siteName}');\n";
                    $configContent .= "define('SITE_URL', '{$siteUrl}');\n";
                    $configContent .= "define('UPLOAD_DIR', __DIR__ . '/uploads');\n\n";
                    $configContent .= "// 创建上传目录（如果不存在）\n";
                    $configContent .= "if (!file_exists(UPLOAD_DIR)) {\n";
                    $configContent .= "    mkdir(UPLOAD_DIR, 0777, true);\n";
                    $configContent .= "}\n\n";
                    $configContent .= "// 会话配置\n";
                    $configContent .= "define('DB_INSTALLED', true);\n";
                    $configContent .= "session_start();\n\n";
                    
                    // 添加数据库连接函数和其他辅助函数
                    $configContent .= file_get_contents('config_template.php');
                    
                    // 保存配置文件
                    file_put_contents('config.php', $configContent);
                    
                    // 检查是否存在备份文件
                    $backup_file = __DIR__ . '/数据库/install_database_backup.sql';
                    if (file_exists($backup_file)) {
                        // 从备份文件恢复数据库
                        $sql = file_get_contents($backup_file);
                        $pdo->exec($sql);
                        logBackupMessage('从备份文件恢复数据库成功');
                    } else {
                        // 初始化数据库表和基础数据
                        require_once 'init_db.php';
                    }
                    
                    // 定义日志函数
                    function logBackupMessage($message) {
                        $logFile = __DIR__ . '/数据库/backup_log.txt';
                        $timestamp = date('Y-m-d H:i:s');
                        $logMessage = "[$timestamp] $message\n";
                        file_put_contents($logFile, $logMessage, FILE_APPEND);
                    }
                    
                    // 设置成功消息并跳转到第三步
                    $success = '数据库配置成功！';
                    $step = 3;
                }
            } catch (PDOException $e) {
                $error = '数据库连接失败：' . $e->getMessage();
            }
        }
    }
}

// 如果需要修复数据库表
if (isset($needFix) && $needFix) {
    $error = '检测到数据库配置正确，但缺少必要的表。请点击下方按钮修复数据库。';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站安装向导</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .wizard-container {
            max-width: 800px;
            margin: 50px auto;
        }
        .wizard-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .wizard-header {
            background-color: #4e73df;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .install-steps {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            position: relative;
        }
        .install-steps::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step {
            position: relative;
            z-index: 2;
            background: white;
            padding: 0 1rem;
            text-align: center;
        }
        .step-number {
            width: 50px;
            height: 50px;
            line-height: 46px;
            border: 2px solid #e9ecef;
            border-radius: 50%;
            display: inline-block;
            background: white;
            margin-bottom: 0.5rem;
            font-weight: bold;
            transition: all 0.3s;
        }
        .step.active .step-number {
            border-color: #4e73df;
            color: #4e73df;
        }
        .step.completed .step-number {
            background: #4e73df;
            border-color: #4e73df;
            color: white;
        }
        .form-control:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }
        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }
        .btn-primary:hover {
            background-color: #3a5fc8;
            border-color: #3a5fc8;
        }
        .success-icon {
            font-size: 80px;
            color: #1cc88a;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .requirement-item:last-child {
            border-bottom: none;
        }
        .check-icon {
            color: #1cc88a;
        }
        .error-icon {
            color: #e74a3b;
        }
    </style>
</head>
<body>
    <div class="container wizard-container">
        <div class="card wizard-card">
            <div class="wizard-header">
                <h2><i class="bi bi-gear-fill me-2"></i>网站安装向导</h2>
                <p class="mb-0">欢迎使用安装向导，我们将帮助您快速配置网站</p>
            </div>
            <div class="card-body p-4">
                <div class="install-steps">
                    <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">
                        <div class="step-number">1</div>
                        <div class="step-title">环境检测</div>
                    </div>
                    <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">
                        <div class="step-number">2</div>
                        <div class="step-title">数据库配置</div>
                    </div>
                    <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">
                        <div class="step-number">3</div>
                        <div class="step-title">安装完成</div>
                    </div>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php echo $error; ?>
                    <?php if (isset($needFix) && $needFix): ?>
                    <div class="mt-3">
                        <a href="fix_database.php" class="btn btn-outline-danger">修复数据库</a>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <?php echo $success; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 1): ?>
                <div class="mb-4">
                    <h4 class="mb-3"><i class="bi bi-hdd-stack me-2"></i>系统环境检测</h4>
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="requirement-item">
                                <div>
                                    <strong>PHP版本</strong>
                                    <div class="text-muted small">需要 PHP 7.4 或更高版本</div>
                                </div>
                                <div>
                                    <span class="badge bg-secondary"><?php echo PHP_VERSION; ?></span>
                                    <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                                    <i class="bi bi-check-circle-fill check-icon ms-2"></i>
                                    <?php else: ?>
                                    <i class="bi bi-x-circle-fill error-icon ms-2"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if (version_compare(PHP_VERSION, '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql') && is_writable(__DIR__)): ?>
                    <div class="d-grid gap-2 mt-4">
                        <a href="?step=2" class="btn btn-primary">下一步</a>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        请确保您的系统满足所有要求后再继续安装。
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 2): ?>
                <div class="mb-4">
                    <h4 class="mb-3"><i class="bi bi-database-fill me-2"></i>数据库配置</h4>
                    <form method="post" action="" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="db_host" class="form-label">数据库主机</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbHost); ?>" required>
                            <div class="form-text">通常为localhost</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">数据库用户名</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbUser); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">数据库密码</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($dbPass); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">数据库名称</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbName); ?>" required>
                            <div class="form-text">如果数据库不存在，系统会自动创建</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_name" class="form-label">网站名称</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_url" class="form-label">网站URL</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo htmlspecialchars($siteUrl); ?>" required>
                            <div class="form-text">包含http://或https://的完整URL</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">开始安装</button>
                            <a href="?step=1" class="btn btn-outline-secondary">返回上一步</a>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 3): ?>
                <div class="text-center mb-4">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill success-icon"></i>
                    </div>
                    <h4 class="mb-3">安装完成！</h4>
                    <p>网站已成功安装并配置完成。</p>
                    <p>您可以使用以下默认管理员账号登录：</p>
                    <div class="card mb-3 mx-auto" style="max-width: 300px;">
                        <div class="card-body text-start">
                            <p class="mb-1"><strong>用户名：</strong> admin</p>
                            <p class="mb-0"><strong>密码：</strong> admin123</p>
                        </div>
                    </div>
                    <p class="text-muted small">请登录后立即修改默认密码！</p>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">进入网站首页</a>
                        <a href="admin/index.php" class="btn btn-outline-secondary ms-2">进入管理后台</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                            <div class="requirement-item">
                                <div>
                                    <strong>PDO扩展</strong>
                                    <div class="text-muted small">用于数据库连接</div>
                                </div>
                                <div>
                                    <?php if (extension_loaded('pdo')): ?>
                                    <span class="badge bg-success">已安装</span>
                                    <i class="bi bi-check-circle-fill check-icon ms-2"></i>
                                    <?php else: ?>
                                    <span class="badge bg-danger">未安装</span>
                                    <i class="bi bi-x-circle-fill error-icon ms-2"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if (version_compare(PHP_VERSION, '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql') && is_writable(__DIR__)): ?>
                    <div class="d-grid gap-2 mt-4">
                        <a href="?step=2" class="btn btn-primary">下一步</a>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        请确保您的系统满足所有要求后再继续安装。
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 2): ?>
                <div class="mb-4">
                    <h4 class="mb-3"><i class="bi bi-database-fill me-2"></i>数据库配置</h4>
                    <form method="post" action="" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="db_host" class="form-label">数据库主机</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbHost); ?>" required>
                            <div class="form-text">通常为localhost</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">数据库用户名</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbUser); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">数据库密码</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($dbPass); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">数据库名称</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbName); ?>" required>
                            <div class="form-text">如果数据库不存在，系统会自动创建</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_name" class="form-label">网站名称</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_url" class="form-label">网站URL</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo htmlspecialchars($siteUrl); ?>" required>
                            <div class="form-text">包含http://或https://的完整URL</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">开始安装</button>
                            <a href="?step=1" class="btn btn-outline-secondary">返回上一步</a>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 3): ?>
                <div class="text-center mb-4">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill success-icon"></i>
                    </div>
                    <h4 class="mb-3">安装完成！</h4>
                    <p>网站已成功安装并配置完成。</p>
                    <p>您可以使用以下默认管理员账号登录：</p>
                    <div class="card mb-3 mx-auto" style="max-width: 300px;">
                        <div class="card-body text-start">
                            <p class="mb-1"><strong>用户名：</strong> admin</p>
                            <p class="mb-0"><strong>密码：</strong> admin123</p>
                        </div>
                    </div>
                    <p class="text-muted small">请登录后立即修改默认密码！</p>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">进入网站首页</a>
                        <a href="admin/index.php" class="btn btn-outline-secondary ms-2">进入管理后台</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                            <div class="requirement-item">
                                <div>
                                    <strong>PDO MySQL扩展</strong>
                                    <div class="text-muted small">用于MySQL数据库连接</div>
                                </div>
                                <div>
                                    <?php if (extension_loaded('pdo_mysql')): ?>
                                    <span class="badge bg-success">已安装</span>
                                    <i class="bi bi-check-circle-fill check-icon ms-2"></i>
                                    <?php else: ?>
                                    <span class="badge bg-danger">未安装</span>
                                    <i class="bi bi-x-circle-fill error-icon ms-2"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if (version_compare(PHP_VERSION, '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql') && is_writable(__DIR__)): ?>
                    <div class="d-grid gap-2 mt-4">
                        <a href="?step=2" class="btn btn-primary">下一步</a>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        请确保您的系统满足所有要求后再继续安装。
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 2): ?>
                <div class="mb-4">
                    <h4 class="mb-3"><i class="bi bi-database-fill me-2"></i>数据库配置</h4>
                    <form method="post" action="" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="db_host" class="form-label">数据库主机</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbHost); ?>" required>
                            <div class="form-text">通常为localhost</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">数据库用户名</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbUser); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">数据库密码</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($dbPass); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">数据库名称</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbName); ?>" required>
                            <div class="form-text">如果数据库不存在，系统会自动创建</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_name" class="form-label">网站名称</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_url" class="form-label">网站URL</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo htmlspecialchars($siteUrl); ?>" required>
                            <div class="form-text">包含http://或https://的完整URL</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">开始安装</button>
                            <a href="?step=1" class="btn btn-outline-secondary">返回上一步</a>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 3): ?>
                <div class="text-center mb-4">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill success-icon"></i>
                    </div>
                    <h4 class="mb-3">安装完成！</h4>
                    <p>网站已成功安装并配置完成。</p>
                    <p>您可以使用以下默认管理员账号登录：</p>
                    <div class="card mb-3 mx-auto" style="max-width: 300px;">
                        <div class="card-body text-start">
                            <p class="mb-1"><strong>用户名：</strong> admin</p>
                            <p class="mb-0"><strong>密码：</strong> admin123</p>
                        </div>
                    </div>
                    <p class="text-muted small">请登录后立即修改默认密码！</p>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">进入网站首页</a>
                        <a href="admin/index.php" class="btn btn-outline-secondary ms-2">进入管理后台</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                            <div class="requirement-item">
                                <div>
                                    <strong>文件写入权限</strong>
                                    <div class="text-muted small">用于创建配置文件和上传目录</div>
                                </div>
                                <div>
                                    <?php if (is_writable(__DIR__)): ?>
                                    <span class="badge bg-success">可写</span>
                                    <i class="bi bi-check-circle-fill check-icon ms-2"></i>
                                    <?php else: ?>
                                    <span class="badge bg-danger">不可写</span>
                                    <i class="bi bi-x-circle-fill error-icon ms-2"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if (version_compare(PHP_VERSION, '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql') && is_writable(__DIR__)): ?>
                    <div class="d-grid gap-2 mt-4">
                        <a href="?step=2" class="btn btn-primary">下一步</a>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        请确保您的系统满足所有要求后再继续安装。
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 2): ?>
                <div class="mb-4">
                    <h4 class="mb-3"><i class="bi bi-database-fill me-2"></i>数据库配置</h4>
                    <form method="post" action="" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="db_host" class="form-label">数据库主机</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbHost); ?>" required>
                            <div class="form-text">通常为localhost</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">数据库用户名</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbUser); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">数据库密码</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($dbPass); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">数据库名称</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbName); ?>" required>
                            <div class="form-text">如果数据库不存在，系统会自动创建</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_name" class="form-label">网站名称</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_url" class="form-label">网站URL</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo htmlspecialchars($siteUrl); ?>" required>
                            <div class="form-text">包含http://或https://的完整URL</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">开始安装</button>
                            <a href="?step=1" class="btn btn-outline-secondary">返回上一步</a>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>
                
                <?php if ($step === 3): ?>
                <div class="text-center mb-4">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill success-icon"></i>
                    </div>
                    <h4 class="mb-3">安装完成！</h4>
                    <p>网站已成功安装并配置完成。</p>
                    <p>您可以使用以下默认管理员账号登录：</p>
                    <div class="card mb-3 mx-auto" style="max-width: 300px;">
                        <div class="card-body text-start">
                            <p class="mb-1"><strong>用户名：</strong> admin</p>
                            <p class="mb-0"><strong>密码：</strong> admin123</p>
                        </div>
                    </div>
                    <p class="text-muted small">请登录后立即修改默认密码！</p>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">进入网站首页</a>
                        <a href="admin/index.php" class="btn btn-outline-secondary ms-2">进入管理后台</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>