<?php
// 包含配置文件
require_once '../config.php';
// 注意：functions.php文件不存在，暂时注释掉
// require_once '../includes/functions.php';

// 获取数据库连接
$conn = getDbConnection();

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    redirect('/login.php');
}

// 处理商品删除请求
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $product_id = (int)$_GET['id'];
    
    // 先检查该商品是否在订单中被引用
    $check_sql = "SELECT COUNT(*) FROM order_items WHERE product_id = :product_id";
    $stmt = $conn->prepare($check_sql);
    $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->execute();
    $referenced = $stmt->fetchColumn() > 0;
    
    if ($referenced) {
        // 如果商品被订单引用，则不能删除
        $_SESSION['error_message'] = "无法删除该商品，因为它已经在订单中被使用。您可以将商品下架而不是删除。";
    } else {
        // 商品未被引用，可以安全删除
        $sql = "DELETE FROM products WHERE id = :product_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            $_SESSION['success_message'] = "商品已成功删除！";
        } else {
            $_SESSION['error_message'] = "删除商品时出错: " . $conn->errorInfo()[2];
        }
    }
    
    // 重定向回商品列表页面
    redirect('/admin/products.php');
}

// 处理商品上下架请求
if (isset($_GET['action']) && ($_GET['action'] == 'enable' || $_GET['action'] == 'disable') && isset($_GET['id'])) {
    $product_id = (int)$_GET['id'];
    $status = ($_GET['action'] == 'enable') ? 1 : 0;
    
    // 更新商品状态
    $sql = "UPDATE products SET is_active = :status WHERE id = :product_id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':status', $status, PDO::PARAM_INT);
    $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        $message = ($status == 1) ? "商品已成功上架！" : "商品已成功下架！";
        $_SESSION['success_message'] = $message;
    } else {
        $_SESSION['error_message'] = "更新商品状态时出错: " . $conn->errorInfo()[2];
    }
    
    // 重定向回商品列表页面
    redirect('/admin/products.php');
}

// 处理商品置顶/取消置顶请求
if (isset($_GET['action']) && ($_GET['action'] == 'set_top' || $_GET['action'] == 'cancel_top') && isset($_GET['id'])) {
    $product_id = (int)$_GET['id'];
    $top_status = ($_GET['action'] == 'set_top') ? 1 : 0;
    
    // 更新商品置顶状态
    $sql = "UPDATE products SET is_top = :top_status, last_top_time = " . ($top_status == 1 ? 'CURRENT_TIMESTAMP' : 'NULL') . " WHERE id = :product_id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':top_status', $top_status, PDO::PARAM_INT);
    $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        $message = ($top_status == 1) ? "商品已成功置顶！" : "商品已取消置顶！";
        $_SESSION['success_message'] = $message;
    } else {
        $_SESSION['error_message'] = "更新商品置顶状态时出错: " . $conn->errorInfo()[2];
    }
    
    // 重定向回商品列表页面
    redirect('/admin/products.php');
}

// 获取所有商品
$sql = "SELECT p.*, c.name as category_name FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        ORDER BY p.is_top DESC, IFNULL(p.last_top_time, '1970-01-01') DESC, p.created_at DESC, p.id DESC";
$result = $conn->query($sql);

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <?php include 'navbar.php'; ?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>商品管理</h1>
        <a href="/admin/add_product.php" class="btn btn-primary">添加商品</a>
    </div>
    
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php 
                echo $_SESSION['success_message']; 
                unset($_SESSION['success_message']);
            ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger">
            <?php 
                echo $_SESSION['error_message']; 
                unset($_SESSION['error_message']);
            ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['info_message'])): ?>
        <div class="alert alert-info">
            <?php 
                echo $_SESSION['info_message']; 
                unset($_SESSION['info_message']);
            ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <h5>商品列表</h5>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>图片</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result->rowCount() > 0): ?>
                        <?php while($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td>
                                    <?php if (!empty($row['image'])): ?>
                                        <img src="/assets/images/<?php echo $row['image']; ?>" alt="<?php echo $row['name']; ?>" width="50">
                                    <?php else: ?>
                                        <span class="text-muted">无图片</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $row['name']; ?></td>
                                <td><?php echo $row['category_name']; ?></td>
                                <td>¥<?php echo number_format($row['price'], 2); ?></td>
                                <td><?php echo $row['stock']; ?></td>
                                <td>
                                    <?php if (isset($row['is_active']) && $row['is_active'] == 1): ?>
                                        <span class="badge bg-success">已上架</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">已下架</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="/admin/edit_product.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <?php if (isset($row['is_active']) && $row['is_active'] == 1): ?>
                                        <a href="/admin/products.php?action=disable&id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning" onclick="return confirm('确定要下架此商品吗？')">
                                            <i class="bi bi-x-circle"></i> 下架
                                        </a>
                                    <?php else: ?>
                                        <a href="/admin/products.php?action=enable&id=<?php echo $row['id']; ?>" class="btn btn-sm btn-success" onclick="return confirm('确定要上架此商品吗？')">
                                            <i class="bi bi-check-circle"></i> 上架
                                        </a>
                                    <?php endif; ?>
                                    <?php if (isset($row['is_top']) && $row['is_top'] == 1): ?>
                                        <a href="/admin/products.php?action=cancel_top&id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" onclick="return confirm('确定要取消置顶此商品吗？')">
                                            <i class="bi bi-arrow-down-circle"></i> 取消置顶
                                        </a>
                                    <?php else: ?>
                                        <a href="/admin/products.php?action=set_top&id=<?php echo $row['id']; ?>" class="btn btn-sm btn-secondary" onclick="return confirm('确定要置顶此商品吗？')">
                                            <i class="bi bi-arrow-up-circle"></i> 置顶
                                        </a>
                                    <?php endif; ?>
                                    <a href="/admin/products.php?action=delete&id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除此商品吗？此操作不可恢复！')">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center">暂无商品</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
// 包含底部模板
include '../templates/admin_footer.php';
?>