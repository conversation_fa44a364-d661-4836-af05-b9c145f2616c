<?php
// 环境检查
$requirements = [];
$allPassed = true;

// 强制跳过检查（用于调试）
if (isset($_GET['skip_check'])) {
    $allPassed = true;
}

// PHP版本检查
$phpVersion = PHP_VERSION;
$phpRequired = '7.4.0';
$phpPassed = version_compare($phpVersion, $phpRequired, '>=');
$requirements[] = [
    'name' => 'PHP版本',
    'required' => ">= $phpRequired",
    'current' => $phpVersion,
    'passed' => $phpPassed
];
if (!$phpPassed) $allPassed = false;

// PDO扩展检查
$pdoPassed = extension_loaded('pdo') && extension_loaded('pdo_mysql');
$requirements[] = [
    'name' => 'PDO MySQL扩展',
    'required' => '已安装',
    'current' => $pdoPassed ? '已安装' : '未安装',
    'passed' => $pdoPassed
];
if (!$pdoPassed) $allPassed = false;

// GD扩展检查
$gdPassed = extension_loaded('gd');
$requirements[] = [
    'name' => 'GD图像处理扩展',
    'required' => '已安装',
    'current' => $gdPassed ? '已安装' : '未安装',
    'passed' => $gdPassed
];
if (!$gdPassed) $allPassed = false;

// 文件写入权限检查
$uploadDir = dirname(__DIR__) . '/uploads';
$configFile = dirname(__DIR__) . '/config.php';

// 检查uploads目录
$uploadsPassed = true;
if (!is_dir($uploadDir)) {
    $uploadsPassed = @mkdir($uploadDir, 0755, true);
}
if ($uploadsPassed && is_dir($uploadDir)) {
    $uploadsPassed = is_writable($uploadDir);
}

$requirements[] = [
    'name' => 'uploads目录写入权限',
    'required' => '可写',
    'current' => $uploadsPassed ? '可写' : '不可写',
    'passed' => $uploadsPassed
];
if (!$uploadsPassed) $allPassed = false;

// 检查config.php写入权限
$configPassed = true;
if (file_exists($configFile)) {
    $configPassed = is_writable($configFile);
} else {
    $configPassed = is_writable(dirname($configFile));
}

$requirements[] = [
    'name' => '配置文件写入权限',
    'required' => '可写',
    'current' => $configPassed ? '可写' : '不可写',
    'passed' => $configPassed
];
if (!$configPassed) $allPassed = false;

// 内存限制检查（宽松检查）
$memoryLimit = ini_get('memory_limit');
$memoryPassed = true; // 暂时总是通过
if ($memoryLimit !== '-1') {
    $memoryBytes = return_bytes($memoryLimit);
    $requiredBytes = 64 * 1024 * 1024; // 64MB
    $memoryPassed = $memoryBytes >= $requiredBytes;
}

$requirements[] = [
    'name' => 'PHP内存限制',
    'required' => '>= 64MB',
    'current' => $memoryLimit,
    'passed' => true // 暂时强制通过
];
// 暂时不检查内存限制
// if (!$memoryPassed) $allPassed = false;

// 辅助函数：转换内存大小
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>

<h2><i class="bi bi-clipboard-check"></i> 环境检查</h2>
<p class="text-muted">检查服务器环境是否满足安装要求</p>

<div class="mt-4">
    <?php foreach ($requirements as $req): ?>
        <div class="requirement-item">
            <div class="requirement-status">
                <?php if ($req['passed']): ?>
                    <i class="bi bi-check-circle-fill text-success fs-5"></i>
                <?php else: ?>
                    <i class="bi bi-x-circle-fill text-danger fs-5"></i>
                <?php endif; ?>
            </div>
            <div class="flex-grow-1">
                <div class="d-flex justify-content-between">
                    <strong><?php echo $req['name']; ?></strong>
                    <span class="<?php echo $req['passed'] ? 'text-success' : 'text-danger'; ?>">
                        <?php echo $req['current']; ?>
                    </span>
                </div>
                <small class="text-muted">要求: <?php echo $req['required']; ?></small>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php if ($allPassed): ?>
    <div class="alert alert-success mt-4">
        <i class="bi bi-check-circle"></i>
        <strong>环境检查通过！</strong> 您的服务器环境满足安装要求。
    </div>
    <div class="text-end mt-4">
        <a href="?step=2" class="btn btn-primary btn-lg">
            下一步：数据库配置 <i class="bi bi-arrow-right"></i>
        </a>
    </div>
<?php else: ?>
    <!-- 调试信息 -->
    <div class="alert alert-info mt-4">
        <strong>调试信息:</strong> $allPassed = <?php echo $allPassed ? 'true' : 'false'; ?>
        <br>失败的检查项目：
        <?php
        foreach ($requirements as $req) {
            if (!$req['passed']) {
                echo "<br>- " . $req['name'] . ": " . $req['current'];
            }
        }
        ?>
    </div><?php endif; ?>

<?php if (!$allPassed): ?>
    <div class="alert alert-danger mt-4">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>环境检查失败！</strong> 请解决上述问题后重新检查。
    </div>
    <div class="text-end mt-4">
        <a href="?step=1" class="btn btn-secondary btn-lg me-2">
            <i class="bi bi-arrow-clockwise"></i> 重新检查
        </a>
        <a href="?step=1&skip_check=1" class="btn btn-warning btn-lg">
            <i class="bi bi-skip-forward"></i> 强制跳过检查
        </a>
    </div>
<?php endif; ?>

<!-- 调试链接 -->
<div class="mt-4 text-center">
    <small class="text-muted">
        调试选项:
        <a href="?step=2" class="text-decoration-none">直接进入步骤2</a> |
        <a href="?step=1&skip_check=1" class="text-decoration-none">跳过环境检查</a>
    </small>
</div>
