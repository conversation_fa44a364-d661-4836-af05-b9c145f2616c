.product-action-buttons {
    display: flex;
    gap: 12px;
    max-width: 400px;
    margin-left: 0;
}

.product-action-buttons .btn {
    flex: 1;
    padding: 12px 16px;
    min-width: 120px;
    max-width: 160px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.3px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.product-action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-action-buttons .btn:hover::before {
    opacity: 1;
}

.product-action-buttons .btn-primary {
    background: linear-gradient(135deg, #4f46e5, #4338ca);
    color: white;
    box-shadow: 
        0 8px 20px rgba(79, 70, 229, 0.28),
        0 2px 4px rgba(79, 70, 229, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.product-action-buttons .btn-primary:hover {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    transform: translateY(-2px);
    box-shadow: 
        0 12px 25px rgba(79, 70, 229, 0.35),
        0 3px 6px rgba(79, 70, 229, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.product-action-buttons .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 
        0 8px 20px rgba(239, 68, 68, 0.28),
        0 2px 4px rgba(239, 68, 68, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.product-action-buttons .btn-danger:hover {
    background: linear-gradient(135deg, #f87171, #ef4444);
    transform: translateY(-2px);
    box-shadow: 
        0 12px 25px rgba(239, 68, 68, 0.35),
        0 3px 6px rgba(239, 68, 68, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.product-action-buttons .btn:disabled {
    background: #e5e7eb;
    color: #9ca3af;
    box-shadow: none;
    cursor: not-allowed;
    transform: none;
}

.product-action-buttons .btn:disabled::before {
    display: none;
}