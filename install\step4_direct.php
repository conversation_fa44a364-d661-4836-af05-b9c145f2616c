<?php
session_start();

// 设置默认配置（如果没有的话）
if (!isset($_SESSION['db_config'])) {
    $_SESSION['db_config'] = [
        'host' => 'localhost',
        'port' => '3306',
        'username' => 'root',
        'password' => '111111',
        'database' => 'software_store',
        'site_name' => '软件商城',
        'site_url' => 'http://127.0.0.10',
        'admin_username' => 'admin',
        'admin_password' => 'admin123',
        'admin_email' => ''
    ];
}

$config = $_SESSION['db_config'];
$installSteps = [];
$installComplete = false;
$hasError = false;

// 执行安装
if (!isset($_SESSION['install_started']) || isset($_POST['reinstall'])) {
    $_SESSION['install_started'] = true;
    
    try {
        // 连接数据库
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $installSteps[] = [
            'step' => '连接数据库',
            'status' => 'success',
            'message' => '数据库连接成功'
        ];
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 创建用户表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `is_admin` tinyint(1) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // 创建分类表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `description` text,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // 创建产品表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(200) NOT NULL,
            `description` text,
            `category_id` int(11) DEFAULT NULL,
            `version` varchar(50) DEFAULT NULL,
            `price` decimal(10,2) DEFAULT 0.00,
            `download_url` varchar(500) DEFAULT NULL,
            `image_url` varchar(500) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `is_top` tinyint(1) DEFAULT 0,
            `last_top_time` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `category_id` (`category_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $installSteps[] = [
            'step' => '创建数据表',
            'status' => 'success',
            'message' => '数据表创建成功'
        ];
        
        // 创建管理员账号
        $adminPasswordHash = password_hash($config['admin_password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO `users` (`username`, `password`, `email`, `is_admin`, `is_active`) VALUES (?, ?, ?, 1, 1) ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email)");
        $stmt->execute([
            $config['admin_username'],
            $adminPasswordHash,
            $config['admin_email'] ?: null
        ]);
        
        $installSteps[] = [
            'step' => '创建管理员账号',
            'status' => 'success',
            'message' => '管理员账号创建成功'
        ];
        
        // 添加示例分类
        $categories = [
            ['办公软件', '提高工作效率的办公类软件'],
            ['开发工具', '程序开发和编程相关工具'],
            ['系统工具', '系统优化和管理工具'],
            ['多媒体', '音视频处理和播放软件'],
            ['安全软件', '系统安全和防护软件']
        ];
        
        foreach ($categories as $cat) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, description) VALUES (?, ?)");
            $stmt->execute($cat);
        }
        
        $installSteps[] = [
            'step' => '添加示例分类',
            'status' => 'success',
            'message' => '示例分类添加成功'
        ];
        
        // 添加示例产品
        $products = [
            ['Microsoft Office 2021', '微软办公套件最新版本', 1, '2021', 999.00, '#', 'https://via.placeholder.com/300x200?text=Office'],
            ['Visual Studio Code', '免费的代码编辑器', 2, '1.85', 0.00, '#', 'https://via.placeholder.com/300x200?text=VSCode'],
            ['CCleaner Pro', '系统清理优化工具', 3, '6.0', 29.99, '#', 'https://via.placeholder.com/300x200?text=CCleaner'],
            ['Adobe Photoshop 2024', '专业图像处理软件', 4, '2024', 299.00, '#', 'https://via.placeholder.com/300x200?text=Photoshop'],
            ['Kaspersky Total Security', '全面安全防护套件', 5, '2024', 89.99, '#', 'https://via.placeholder.com/300x200?text=Kaspersky']
        ];
        
        foreach ($products as $product) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO products (name, description, category_id, version, price, download_url, image_url) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($product);
        }
        
        $installSteps[] = [
            'step' => '添加示例产品',
            'status' => 'success',
            'message' => '示例产品添加成功'
        ];
        
        // 生成配置文件
        $configContent = "<?php
// 数据库配置
define('DB_HOST', '{$config['host']}');
define('DB_USER', '{$config['username']}');
define('DB_PASS', '{$config['password']}');
define('DB_NAME', '{$config['database']}');
define('DB_PORT', '{$config['port']}');

// 网站配置
define('SITE_NAME', '{$config['site_name']}');
define('SITE_URL', '{$config['site_url']}');

// 安全配置
define('SALT', '" . bin2hex(random_bytes(32)) . "');

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 数据库连接函数
function getDbConnection() {
    static \$pdo = null;
    
    if (\$pdo === null) {
        try {
            \$dsn = \"mysql:host=\" . DB_HOST . \";port=\" . DB_PORT . \";dbname=\" . DB_NAME . \";charset=utf8mb4\";
            \$pdo = new PDO(\$dsn, DB_USER, DB_PASS);
            \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException \$e) {
            die(\"数据库连接失败: \" . \$e->getMessage());
        }
    }
    
    return \$pdo;
}
?>";
        
        $configFile = dirname(__DIR__) . '/config.php';
        if (file_put_contents($configFile, $configContent)) {
            $installSteps[] = [
                'step' => '生成配置文件',
                'status' => 'success',
                'message' => '配置文件生成成功'
            ];
        } else {
            throw new Exception('无法写入配置文件');
        }
        
        // 提交事务
        $pdo->commit();
        
        $installSteps[] = [
            'step' => '完成安装',
            'status' => 'success',
            'message' => '数据库安装完成'
        ];
        
        $installComplete = true;
        $_SESSION['install_success'] = true;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollback();
        }
        $installSteps[] = [
            'step' => '安装失败',
            'status' => 'error',
            'message' => $e->getMessage()
        ];
        $hasError = true;
    }
} else {
    // 已经安装过了
    $installComplete = true;
    $installSteps[] = [
        'step' => '检查安装状态',
        'status' => 'success',
        'message' => '数据库已安装'
    ];
}

// 处理继续到下一步
if (isset($_POST['continue']) && $installComplete) {
    header('Location: index.php?step=5');
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装数据库 - 软件商城</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .step-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .step-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .text-center {
            text-align: center;
        }
        .mt-4 {
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 安装数据库</h1>
        
        <div id="install-progress">
            <?php foreach ($installSteps as $step): ?>
                <div class="step <?php echo $step['status'] === 'success' ? 'step-success' : 'step-error'; ?>">
                    <strong><?php echo htmlspecialchars($step['step']); ?>:</strong>
                    <?php echo htmlspecialchars($step['message']); ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if ($installComplete && !$hasError): ?>
            <div class="step step-success">
                <strong>🎉 安装成功！</strong>
                <p>数据库安装完成，系统已准备就绪。</p>
                <p><strong>管理员账号：</strong><?php echo htmlspecialchars($config['admin_username']); ?></p>
                <p><strong>管理员密码：</strong><?php echo htmlspecialchars($config['admin_password']); ?></p>
            </div>
            
            <div class="text-center mt-4">
                <form method="post" style="display: inline;">
                    <button type="submit" name="continue" class="btn btn-success">
                        完成安装 →
                    </button>
                </form>
                <a href="../index.php" class="btn btn-primary">直接访问网站</a>
            </div>
        <?php elseif ($hasError): ?>
            <div class="text-center mt-4">
                <form method="post" style="display: inline;">
                    <button type="submit" name="reinstall" class="btn btn-danger">
                        重新安装
                    </button>
                </form>
                <a href="step2.php" class="btn btn-primary">修改配置</a>
            </div>
        <?php endif; ?>
        
        <div class="text-center mt-4">
            <p><small>
                <a href="index.php?step=3">← 上一步</a> | 
                <a href="../index.php">访问网站</a> |
                <a href="quick_install.php">快速安装</a>
            </small></p>
        </div>
    </div>
</body>
</html>
