<?php
session_start();

// 处理快速安装
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 设置默认配置
    $_SESSION['db_config'] = [
        'host' => trim($_POST['db_host'] ?? 'localhost'),
        'port' => trim($_POST['db_port'] ?? '3306'),
        'username' => trim($_POST['db_username'] ?? 'root'),
        'password' => $_POST['db_password'] ?? '111111',
        'database' => trim($_POST['db_database'] ?? 'software_store'),
        'site_name' => trim($_POST['site_name'] ?? '软件商城'),
        'site_url' => trim($_POST['site_url'] ?? 'http://127.0.0.10'),
        'admin_username' => trim($_POST['admin_username'] ?? 'admin'),
        'admin_password' => $_POST['admin_password'] ?? 'admin123',
        'admin_email' => trim($_POST['admin_email'] ?? '')
    ];
    
    $config = $_SESSION['db_config'];
    $errors = [];
    $success = false;
    
    // 测试数据库连接
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // 连接到新数据库
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建基本表结构
        $sql = "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `role` enum('admin','user') DEFAULT 'user',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        
        CREATE TABLE IF NOT EXISTS `categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `description` text,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        
        CREATE TABLE IF NOT EXISTS `software` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(200) NOT NULL,
            `description` text,
            `category_id` int(11) DEFAULT NULL,
            `version` varchar(50) DEFAULT NULL,
            `price` decimal(10,2) DEFAULT '0.00',
            `download_url` varchar(500) DEFAULT NULL,
            `image_url` varchar(500) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `category_id` (`category_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $pdo->exec($sql);
        
        // 创建管理员账号
        $adminPassword = password_hash($config['admin_password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, email, role) VALUES (?, ?, ?, 'admin')");
        $stmt->execute([$config['admin_username'], $adminPassword, $config['admin_email']]);
        
        // 生成配置文件
        $configContent = "<?php
// 数据库配置
define('DB_HOST', '{$config['host']}');
define('DB_USER', '{$config['username']}');
define('DB_PASS', '{$config['password']}');
define('DB_NAME', '{$config['database']}');
define('DB_PORT', '{$config['port']}');

// 网站配置
define('SITE_NAME', '{$config['site_name']}');
define('SITE_URL', '{$config['site_url']}');

// 安全配置
define('SALT', '" . bin2hex(random_bytes(32)) . "');

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>";
        
        $configFile = dirname(__DIR__) . '/config.php';
        if (file_put_contents($configFile, $configContent)) {
            $success = true;
        } else {
            $errors[] = '无法写入配置文件';
        }
        
    } catch (Exception $e) {
        $errors[] = '数据库错误: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速安装 - 软件商城</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 软件商城快速安装</h1>
        
        <?php if (isset($success) && $success): ?>
            <div class="alert alert-success">
                <h3>✅ 安装成功！</h3>
                <p>软件商城已成功安装并配置完成。</p>
                <p><strong>管理员账号：</strong><?php echo htmlspecialchars($config['admin_username']); ?></p>
                <p><strong>管理员密码：</strong><?php echo htmlspecialchars($config['admin_password']); ?></p>
                <p><a href="../index.php" style="color: #155724; font-weight: bold;">立即访问网站 →</a></p>
            </div>
        <?php elseif (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h3>❌ 安装失败</h3>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="post">
            <div class="section">
                <h3>📊 数据库配置</h3>
                <div class="form-group">
                    <label>数据库主机:</label>
                    <input type="text" name="db_host" value="localhost" required>
                </div>
                <div class="form-group">
                    <label>数据库端口:</label>
                    <input type="text" name="db_port" value="3306">
                </div>
                <div class="form-group">
                    <label>数据库用户名:</label>
                    <input type="text" name="db_username" value="root" required>
                </div>
                <div class="form-group">
                    <label>数据库密码:</label>
                    <input type="password" name="db_password" value="111111">
                </div>
                <div class="form-group">
                    <label>数据库名:</label>
                    <input type="text" name="db_database" value="software_store" required>
                </div>
            </div>
            
            <div class="section">
                <h3>🌐 网站配置</h3>
                <div class="form-group">
                    <label>网站名称:</label>
                    <input type="text" name="site_name" value="软件商城">
                </div>
                <div class="form-group">
                    <label>网站地址:</label>
                    <input type="text" name="site_url" value="http://127.0.0.10">
                </div>
            </div>
            
            <div class="section">
                <h3>👤 管理员账号</h3>
                <div class="form-group">
                    <label>管理员用户名:</label>
                    <input type="text" name="admin_username" value="admin" required>
                </div>
                <div class="form-group">
                    <label>管理员密码:</label>
                    <input type="password" name="admin_password" value="admin123" required>
                </div>
                <div class="form-group">
                    <label>管理员邮箱:</label>
                    <input type="email" name="admin_email" value="">
                </div>
            </div>
            
            <button type="submit" class="btn">🚀 开始安装</button>
        </form>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p><a href="simple.php">使用向导安装</a> | <a href="../index.php">返回网站</a></p>
        </div>
    </div>
</body>
</html>
