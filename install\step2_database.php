<?php
// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $_SESSION['db_config'] = [
        'host' => trim($_POST['db_host'] ?? 'localhost'),
        'port' => trim($_POST['db_port'] ?? '3306'),
        'username' => trim($_POST['db_username'] ?? ''),
        'password' => $_POST['db_password'] ?? '',
        'database' => trim($_POST['db_database'] ?? ''),
        'site_name' => trim($_POST['site_name'] ?? '软件商城'),
        'site_url' => trim($_POST['site_url'] ?? 'http://localhost'),
        'admin_username' => trim($_POST['admin_username'] ?? 'admin'),
        'admin_password' => $_POST['admin_password'] ?? '',
        'admin_email' => trim($_POST['admin_email'] ?? '')
    ];
    
    // 验证必填字段
    $errors = [];
    if (empty($_SESSION['db_config']['username'])) {
        $errors[] = '数据库用户名不能为空';
    }
    if (empty($_SESSION['db_config']['database'])) {
        $errors[] = '数据库名不能为空';
    }
    if (empty($_SESSION['db_config']['admin_password'])) {
        $errors[] = '管理员密码不能为空';
    }
    if (strlen($_SESSION['db_config']['admin_password']) < 6) {
        $errors[] = '管理员密码至少6位';
    }
    
    if (empty($errors)) {
        header('Location: ?step=3');
        exit;
    }
} else {
    // 设置默认值
    if (!isset($_SESSION['db_config'])) {
        $_SESSION['db_config'] = [
            'host' => 'localhost',
            'port' => '3306',
            'username' => 'root',
            'password' => '',
            'database' => 'software_store',
            'site_name' => '软件商城',
            'site_url' => 'http://localhost',
            'admin_username' => 'admin',
            'admin_password' => '',
            'admin_email' => ''
        ];
    }
}

$config = $_SESSION['db_config'];
?>

<h2><i class="bi bi-database"></i> 数据库配置</h2>
<p class="text-muted">请输入数据库连接信息和网站基本配置</p>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form method="post" class="mt-4">
    <!-- 数据库配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-server"></i> 数据库连接信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">数据库服务器地址</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" 
                               value="<?php echo htmlspecialchars($config['host']); ?>" required>
                        <div class="form-text">通常为 localhost 或 127.0.0.1</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="db_port" class="form-label">端口</label>
                        <input type="number" class="form-control" id="db_port" name="db_port" 
                               value="<?php echo htmlspecialchars($config['port']); ?>" min="1" max="65535">
                        <div class="form-text">默认 3306</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_username" class="form-label">数据库用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="db_username" name="db_username" 
                               value="<?php echo htmlspecialchars($config['username']); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_password" class="form-label">数据库密码</label>
                        <input type="password" class="form-control" id="db_password" name="db_password" 
                               value="<?php echo htmlspecialchars($config['password']); ?>">
                        <div class="form-text">如果没有密码请留空</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="db_database" class="form-label">数据库名 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="db_database" name="db_database" 
                       value="<?php echo htmlspecialchars($config['database']); ?>" required>
                <div class="form-text">如果数据库不存在，安装程序会尝试创建</div>
            </div>
        </div>
    </div>

    <!-- 网站配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-globe"></i> 网站基本信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">网站名称</label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="<?php echo htmlspecialchars($config['site_name']); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_url" class="form-label">网站地址</label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="<?php echo htmlspecialchars($config['site_url']); ?>">
                        <div class="form-text">包含 http:// 或 https://</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员账号 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-person-gear"></i> 管理员账号</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="admin_username" class="form-label">管理员用户名</label>
                        <input type="text" class="form-control" id="admin_username" name="admin_username" 
                               value="<?php echo htmlspecialchars($config['admin_username']); ?>" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">管理员密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" 
                               value="<?php echo htmlspecialchars($config['admin_password']); ?>" required minlength="6">
                        <div class="form-text">至少6位字符</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">管理员邮箱</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" 
                               value="<?php echo htmlspecialchars($config['admin_email']); ?>">
                        <div class="form-text">可选</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between">
        <a href="?step=1" class="btn btn-secondary btn-lg">
            <i class="bi bi-arrow-left"></i> 上一步
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            下一步：测试连接 <i class="bi bi-arrow-right"></i>
        </button>
    </div>
</form>
