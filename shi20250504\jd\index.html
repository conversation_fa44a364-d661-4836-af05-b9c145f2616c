<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类导航</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #e60000;
            --primary-light: #ff5252;
            --primary-dark: #b30000;
            --accent-color: #ff9e80;
            --text-light: #ffffff;
            --shadow-color: rgba(204, 0, 0, 0.3);
        }
        
        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-image: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
            width: 100%;
        }
        
        .nav-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            list-style: none;
            perspective: 1000px;
        }
        
        .nav-item {
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            border-radius: 16px;
            box-shadow: 0 10px 20px var(--shadow-color);
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
            backdrop-filter: blur(5px);
            transform-style: preserve-3d;
        }
        
        .nav-item:hover {
            transform: translateY(-10px) rotateX(5deg);
            box-shadow: 0 15px 30px var(--shadow-color);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-color: rgba(255, 255, 255, 0.25);
        }
        
        .nav-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
            pointer-events: none;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 24px;
            color: var(--text-light);
            text-decoration: none;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--accent-color), var(--primary-light));
            opacity: 0;
            transition: all 0.5s ease;
            z-index: -1;
            box-shadow: inset 0 0 15px rgba(255, 255, 255, 0.3);
            transform: scale(0.9);
            border-radius: 12px;
        }
        
        .nav-item:hover .nav-link::before {
            opacity: 0.8;
            transform: scale(1);
        }
        
        .nav-item:active {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }
        
        @media (max-width: 768px) {
            .nav-list {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .nav-container {
                margin: 20px auto;
            }
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 10px 20px var(--shadow-color); }
            50% { box-shadow: 0 15px 30px var(--shadow-color); }
            100% { box-shadow: 0 10px 20px var(--shadow-color); }
        }
        
        .nav-item:nth-child(odd) {
            animation: pulse 3s infinite alternate;
            animation-delay: calc(var(--n) * 0.5s);
        }
        
        .nav-item:nth-child(1) { --n: 1; }
        .nav-item:nth-child(2) { --n: 2; }
        .nav-item:nth-child(3) { --n: 3; }
        .nav-item:nth-child(4) { --n: 4; }
        .nav-item:nth-child(5) { --n: 5; }
        .nav-item:nth-child(6) { --n: 6; }
    </style>
</head>
<body>
    <nav class="nav-container">
        <ul class="nav-list">
            <li class="nav-item">
                <a href="#" class="nav-link">首页</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">产品中心</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">解决方案</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">新闻资讯</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">关于我们</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">联系我们</a>
            </li>
        </ul>
    </nav>
</body>
</html>