<?php
// 检查是否有数据库配置
if (!isset($_SESSION['db_config'])) {
    header('Location: ?step=2');
    exit;
}

$config = $_SESSION['db_config'];
$testResults = [];
$canProceed = false;

// 测试数据库连接
try {
    // 构建DSN
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
    
    $testResults[] = [
        'test' => '连接到MySQL服务器',
        'status' => 'testing',
        'message' => '正在连接...'
    ];
    
    // 尝试连接MySQL服务器
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $testResults[0] = [
        'test' => '连接到MySQL服务器',
        'status' => 'success',
        'message' => '连接成功'
    ];
    
    // 检查数据库是否存在
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        $testResults[] = [
            'test' => '检查数据库',
            'status' => 'warning',
            'message' => "数据库 '{$config['database']}' 已存在，安装将覆盖现有数据"
        ];
    } else {
        $testResults[] = [
            'test' => '检查数据库',
            'status' => 'info',
            'message' => "数据库 '{$config['database']}' 不存在，将创建新数据库"
        ];
    }
    
    // 尝试创建数据库（如果不存在）
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $testResults[] = [
            'test' => '创建/访问数据库',
            'status' => 'success',
            'message' => '数据库准备就绪'
        ];
        
        // 选择数据库
        $pdo->exec("USE `{$config['database']}`");
        
        // 测试创建表权限
        $testTable = 'install_test_' . time();
        $pdo->exec("CREATE TABLE `$testTable` (id INT PRIMARY KEY AUTO_INCREMENT, test VARCHAR(50))");
        $pdo->exec("DROP TABLE `$testTable`");
        
        $testResults[] = [
            'test' => '数据库权限测试',
            'status' => 'success',
            'message' => '具有创建表的权限'
        ];
        
        $canProceed = true;
        
    } catch (PDOException $e) {
        $testResults[] = [
            'test' => '数据库权限测试',
            'status' => 'error',
            'message' => '权限不足: ' . $e->getMessage()
        ];
    }
    
} catch (PDOException $e) {
    $testResults[0] = [
        'test' => '连接到MySQL服务器',
        'status' => 'error',
        'message' => '连接失败: ' . $e->getMessage()
    ];
}

// 处理继续安装
if (isset($_POST['proceed']) && $canProceed) {
    header('Location: ?step=4');
    exit;
}
?>

<h2><i class="bi bi-wifi"></i> 数据库连接测试</h2>
<p class="text-muted">正在测试数据库连接和权限...</p>

<div class="mt-4">
    <?php foreach ($testResults as $result): ?>
        <div class="d-flex align-items-center mb-3 p-3 border rounded">
            <div class="me-3">
                <?php
                switch ($result['status']) {
                    case 'success':
                        echo '<i class="bi bi-check-circle-fill text-success fs-4"></i>';
                        break;
                    case 'error':
                        echo '<i class="bi bi-x-circle-fill text-danger fs-4"></i>';
                        break;
                    case 'warning':
                        echo '<i class="bi bi-exclamation-triangle-fill text-warning fs-4"></i>';
                        break;
                    case 'info':
                        echo '<i class="bi bi-info-circle-fill text-info fs-4"></i>';
                        break;
                    case 'testing':
                        echo '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
                        break;
                }
                ?>
            </div>
            <div class="flex-grow-1">
                <h6 class="mb-1"><?php echo htmlspecialchars($result['test']); ?></h6>
                <p class="mb-0 text-muted"><?php echo htmlspecialchars($result['message']); ?></p>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- 配置信息摘要 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-info-circle"></i> 配置信息摘要</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>数据库服务器:</strong> <?php echo htmlspecialchars($config['host'] . ':' . $config['port']); ?></p>
                <p><strong>数据库名:</strong> <?php echo htmlspecialchars($config['database']); ?></p>
                <p><strong>数据库用户:</strong> <?php echo htmlspecialchars($config['username']); ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>网站名称:</strong> <?php echo htmlspecialchars($config['site_name']); ?></p>
                <p><strong>网站地址:</strong> <?php echo htmlspecialchars($config['site_url']); ?></p>
                <p><strong>管理员用户名:</strong> <?php echo htmlspecialchars($config['admin_username']); ?></p>
            </div>
        </div>
    </div>
</div>

<?php if ($canProceed): ?>
    <div class="alert alert-success mt-4">
        <i class="bi bi-check-circle"></i>
        <strong>数据库连接测试通过！</strong> 可以开始安装数据库。
    </div>
    
    <form method="post" class="mt-4">
        <div class="d-flex justify-content-between">
            <a href="?step=2" class="btn btn-secondary btn-lg">
                <i class="bi bi-arrow-left"></i> 修改配置
            </a>
            <button type="submit" name="proceed" class="btn btn-success btn-lg">
                开始安装数据库 <i class="bi bi-arrow-right"></i>
            </button>
        </div>
    </form>
<?php else: ?>
    <div class="alert alert-danger mt-4">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>数据库连接测试失败！</strong> 请检查配置信息并重试。
    </div>
    
    <div class="text-end mt-4">
        <a href="?step=2" class="btn btn-primary btn-lg">
            <i class="bi bi-arrow-left"></i> 返回修改配置
        </a>
    </div>
<?php endif; ?>

<?php if (isset($testResults) && count($testResults) > 0 && $testResults[0]['status'] === 'testing'): ?>
<script>
// 自动刷新页面以更新测试状态
setTimeout(function() {
    location.reload();
}, 2000);
</script>
<?php endif; ?>
