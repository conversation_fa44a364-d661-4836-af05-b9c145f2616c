.custom-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-confirm-modal.show {
    opacity: 1;
}

.custom-confirm-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
    border-radius: 20px;
    padding: 2.5rem;
    max-width: 90%;
    width: 420px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    transform: scale(0.95) translateY(-20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.custom-confirm-modal.show .custom-confirm-content {
    transform: scale(1) translateY(0);
}

.custom-confirm-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1.25rem;
    text-align: center;
    letter-spacing: -0.025em;
}

.custom-confirm-message {
    font-size: 1.125rem;
    color: #4a5568;
    margin-bottom: 2rem;
    text-align: center;
    line-height: 1.6;
}

.custom-confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 1.25rem;
}

.custom-confirm-btn {
    padding: 0.875rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 120px;
}

.custom-confirm-btn-cancel {
    background-color: #edf2f7;
    color: #4a5568;
}

.custom-confirm-btn-cancel:hover {
    background-color: #e2e8f0;
    transform: translateY(-1px);
}

.custom-confirm-btn-confirm {
    background: linear-gradient(135deg, #4f46e5, #3b82f6);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.custom-confirm-btn-confirm:hover {
    background: linear-gradient(135deg, #4338ca, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.95) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}