<?php
require_once 'config.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    setMessage('warning', '请先登录');
    redirect('login.php');
}

$userId = getCurrentUserId();
$pdo = getDbConnection();

// 获取购物车商品
$stmt = $pdo->prepare("SELECT c.id, c.quantity, p.id as product_id, p.name, p.price, p.stock, p.image_url 
                      FROM cart_items c 
                      JOIN products p ON c.product_id = p.id 
                      WHERE c.user_id = :user_id");
$stmt->execute(['user_id' => $userId]);
$cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 如果购物车为空，重定向到购物车页面
if (empty($cartItems)) {
    setMessage('warning', '您的购物车是空的，请先添加商品');
    redirect('cart.php');
}

// 计算总价
$totalPrice = 0;
foreach ($cartItems as $item) {
    $totalPrice += $item['price'] * $item['quantity'];
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = :id");
$stmt->execute(['id' => $userId]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// 处理订单提交
if (isset($_POST['place_order'])) {
    $shippingAddress = trim($_POST['shipping_address']);
    $paymentMethod = $_POST['payment_method'];
    
    // 验证数据
    $errors = [];
    if (empty($shippingAddress)) {
        $errors[] = '请填写收货地址';
    }
    if (!in_array($paymentMethod, ['alipay', 'wechat'])) {
        $errors[] = '请选择有效的支付方式';
    }
    
    // 检查库存
    $stockError = false;
    foreach ($cartItems as $item) {
        $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = :id");
        $stmt->execute(['id' => $item['product_id']]);
        $currentStock = $stmt->fetchColumn();
        
        if ($currentStock < $item['quantity']) {
            $stockError = true;
            $errors[] = '商品「' . htmlspecialchars($item['name']) . '」库存不足，当前库存：' . $currentStock;
        }
    }
    
    if (empty($errors)) {
        try {
            // 开始事务
            $pdo->beginTransaction();
            
            // 创建订单
            $stmt = $pdo->prepare("INSERT INTO orders (user_id, total_amount, status, shipping_address, payment_method) 
                                  VALUES (:user_id, :total_amount, 'pending', :shipping_address, :payment_method)");
            $stmt->execute([
                'user_id' => $userId,
                'total_amount' => $totalPrice,
                'shipping_address' => $shippingAddress,
                'payment_method' => $paymentMethod
            ]);
            
            $orderId = $pdo->lastInsertId();
            
            // 添加订单项
            foreach ($cartItems as $item) {
                $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) 
                                      VALUES (:order_id, :product_id, :quantity, :price)");
                $stmt->execute([
                    'order_id' => $orderId,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price']
                ]);
                
                // 减少库存
                $stmt = $pdo->prepare("UPDATE products SET stock = stock - :quantity WHERE id = :id");
                $stmt->execute([
                    'quantity' => $item['quantity'],
                    'id' => $item['product_id']
                ]);
            }
            
            // 清空购物车
            $stmt = $pdo->prepare("DELETE FROM cart_items WHERE user_id = :user_id");
            $stmt->execute(['user_id' => $userId]);
            
            // 提交事务
            $pdo->commit();
            
            // 重定向到支付页面
            redirect('payment.php?order_id=' . $orderId);
            
        } catch (PDOException $e) {
            // 回滚事务
            $pdo->rollBack();
            setMessage('danger', '创建订单失败，请稍后重试');
        }
    } else {
        // 显示错误信息
        foreach ($errors as $error) {
            setMessage('danger', $error);
        }
    }
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结算 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .checkout-img {
            max-width: 60px;
            max-height: 60px;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <!-- 消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <h1 class="mb-4">结算</h1>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">订单信息</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label for="shipping_address" class="form-label">收货地址</label>
                                <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">支付方式</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="alipay" value="alipay" checked>
                                    <label class="form-check-label" for="alipay">
                                        <i class="bi bi-credit-card text-primary"></i> 支付宝
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" id="wechat" value="wechat">
                                    <label class="form-check-label" for="wechat">
                                        <i class="bi bi-chat-square text-success"></i> 微信支付
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" name="place_order" class="btn btn-primary btn-lg w-100">
                                <i class="bi bi-credit-card"></i> 提交订单
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">订单摘要</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>商品</th>
                                        <th>数量</th>
                                        <th class="text-end">小计</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cartItems as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo !empty($item['image_url']) ? htmlspecialchars($item['image_url']) : 'uploads/default-product.jpg'; ?>" 
                                                         class="checkout-img me-2" alt="<?php echo htmlspecialchars($item['name']); ?>">
                                                    <div class="small"><?php echo htmlspecialchars($item['name']); ?></div>
                                                </div>
                                            </td>
                                            <td><?php echo $item['quantity']; ?></td>
                                            <td class="text-end">¥<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="2" class="text-end fw-bold">总计：</td>
                                        <td class="text-end fw-bold text-danger">¥<?php echo number_format($totalPrice, 2); ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        <a href="cart.php" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-arrow-left"></i> 返回购物车
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>