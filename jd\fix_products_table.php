<?php
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // 检查products表是否存在is_active字段
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'is_active'");
    $isActiveExists = $stmt->fetch();
    
    // 检查products表是否存在updated_at字段
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'updated_at'");
    $updatedAtExists = $stmt->fetch();
    
    // 如果is_active字段不存在，添加它
    if (!$isActiveExists) {
        $pdo->exec("ALTER TABLE products ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER image_url");
        echo "已添加is_active字段到products表<br>";
    } else {
        echo "is_active字段已存在<br>";
    }
    
    // 如果updated_at字段不存在，添加它
    if (!$updatedAtExists) {
        $pdo->exec("ALTER TABLE products ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        echo "已添加updated_at字段到products表<br>";
    } else {
        echo "updated_at字段已存在<br>";
    }
    
    echo "数据库修复完成！";
    
} catch(PDOException $e) {
    die("数据库修复失败: " . $e->getMessage());
}