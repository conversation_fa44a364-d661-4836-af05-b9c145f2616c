/**
 * 用户管理页面对话框覆盖脚本
 * 用于禁用用户管理页面中的确认对话框
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取当前页面URL路径
    const currentPath = window.location.pathname;
    
    // 仅在用户管理页面执行
    if (currentPath.includes('/admin/users.php')) {
        // 查找所有表单
        const forms = document.querySelectorAll('form');
        
        // 移除所有表单上可能被admin-dialog.js添加的事件监听器
        forms.forEach(form => {
            // 克隆表单以移除所有事件监听器
            const newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);
            
            // 为新表单添加直接提交行为
            newForm.addEventListener('submit', function() {
                // 直接提交表单，不显示确认对话框
                return true;
            });
        });
        
        console.log('用户管理页面的确认对话框已禁用');
    }
});