<?php
session_start();

echo "<h2>表单测试</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST数据接收成功:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // 设置session数据
    $_SESSION['db_config'] = [
        'host' => trim($_POST['db_host'] ?? 'localhost'),
        'port' => trim($_POST['db_port'] ?? '3306'),
        'username' => trim($_POST['db_username'] ?? ''),
        'password' => $_POST['db_password'] ?? '',
        'database' => trim($_POST['db_database'] ?? ''),
        'site_name' => trim($_POST['site_name'] ?? '软件商城'),
        'site_url' => trim($_POST['site_url'] ?? 'http://127.0.0.10'),
        'admin_username' => trim($_POST['admin_username'] ?? 'admin'),
        'admin_password' => $_POST['admin_password'] ?? '',
        'admin_email' => trim($_POST['admin_email'] ?? '')
    ];
    
    echo "<h3>Session数据已设置:</h3>";
    echo "<pre>" . print_r($_SESSION['db_config'], true) . "</pre>";
    
    echo "<p><a href='?step=3'>进入步骤3</a></p>";
} else {
    echo "<h3>测试表单:</h3>";
    ?>
    <form method="post">
        <table border="1">
            <tr><td>数据库主机:</td><td><input type="text" name="db_host" value="localhost" required></td></tr>
            <tr><td>数据库端口:</td><td><input type="text" name="db_port" value="3306"></td></tr>
            <tr><td>数据库用户名:</td><td><input type="text" name="db_username" value="root" required></td></tr>
            <tr><td>数据库密码:</td><td><input type="password" name="db_password" value="111111"></td></tr>
            <tr><td>数据库名:</td><td><input type="text" name="db_database" value="test_install" required></td></tr>
            <tr><td>网站名称:</td><td><input type="text" name="site_name" value="软件商城"></td></tr>
            <tr><td>网站地址:</td><td><input type="text" name="site_url" value="http://127.0.0.10"></td></tr>
            <tr><td>管理员用户名:</td><td><input type="text" name="admin_username" value="admin"></td></tr>
            <tr><td>管理员密码:</td><td><input type="password" name="admin_password" value="admin123" required></td></tr>
            <tr><td>管理员邮箱:</td><td><input type="email" name="admin_email" value=""></td></tr>
        </table>
        <p><button type="submit">提交测试</button></p>
    </form>
    <?php
}

echo "<h3>当前Session数据:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<p><a href='index.php?step=2'>返回步骤2</a></p>";
?>
