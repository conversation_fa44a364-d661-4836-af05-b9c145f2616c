<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取统计数据
// 1. 订单总数
$stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
$orderCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// 2. 总销售额
$stmt = $pdo->query("SELECT SUM(total_amount) as total FROM orders WHERE status != 'cancelled'");
$totalSales = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

// 3. 商品总数
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
$productCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// 4. 用户总数
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE is_admin = 0");
$userCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// 5. 最近订单
$stmt = $pdo->query("SELECT o.*, u.username FROM orders o JOIN users u ON o.user_id = u.id ORDER BY o.created_at DESC LIMIT 5");
$recentOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .dashboard-card {
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container">
        <!-- 显示消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endforeach; ?>

        <h1 class="mb-4">仪表盘</h1>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card h-100 border-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">订单总数</h6>
                                <h2 class="card-title mb-0"><?php echo $orderCount; ?></h2>
                            </div>
                            <div class="stat-icon text-primary">
                                <i class="bi bi-receipt"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="orders.php" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card h-100 border-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">总销售额</h6>
                                <h2 class="card-title mb-0">¥<?php echo number_format($totalSales, 2); ?></h2>
                            </div>
                            <div class="stat-icon text-success">
                                <i class="bi bi-currency-yen"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="orders.php" class="btn btn-sm btn-outline-success">查看详情</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card h-100 border-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">商品总数</h6>
                                <h2 class="card-title mb-0"><?php echo $productCount; ?></h2>
                            </div>
                            <div class="stat-icon text-info">
                                <i class="bi bi-box-seam"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="products.php" class="btn btn-sm btn-outline-info">查看详情</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card h-100 border-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">用户总数</h6>
                                <h2 class="card-title mb-0"><?php echo $userCount; ?></h2>
                            </div>
                            <div class="stat-icon text-warning">
                                <i class="bi bi-people"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="users.php" class="btn btn-sm btn-outline-warning">查看详情</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近订单 -->
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">最近订单</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recentOrders)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">暂无订单数据</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td><?php echo $order['id']; ?></td>
                                        <td><?php echo htmlspecialchars($order['username']); ?></td>
                                        <td>¥<?php echo number_format($order['total_amount'], 2); ?></td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            
                                            switch ($order['status']) {
                                                case 'pending':
                                                    $statusClass = 'warning';
                                                    $statusText = '待付款';
                                                    break;
                                                case 'paid':
                                                    $statusClass = 'info';
                                                    $statusText = '已付款';
                                                    break;
                                                case 'shipped':
                                                    $statusClass = 'primary';
                                                    $statusText = '已发货';
                                                    break;
                                                case 'completed':
                                                    $statusClass = 'success';
                                                    $statusText = '已完成';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'danger';
                                                    $statusText = '已取消';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusText = '未知';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                        <td>
                                            <a href="order_detail.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-primary">查看</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-3">
                    <a href="orders.php" class="btn btn-primary">查看所有订单</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>