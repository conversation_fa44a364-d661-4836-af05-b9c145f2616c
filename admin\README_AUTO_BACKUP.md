# 数据库自动备份设置说明

本文档提供如何设置数据库自动备份的详细说明。系统已经实现了两个功能：

1. **手动数据库备份与恢复**：管理员可以在用户管理页面手动导出数据库备份，也可以通过上传备份文件恢复数据库。
2. **自动数据库备份**：通过设置计划任务，系统可以每24小时自动备份一次数据库。

## 自动备份设置步骤（Windows系统）

### 使用Windows任务计划程序设置自动备份

1. 打开Windows任务计划程序（可以在开始菜单搜索"任务计划程序"）
2. 点击右侧操作面板中的"创建基本任务"
3. 输入任务名称，例如"京东商城数据库自动备份"，点击"下一步"
4. 选择"每天"，点击"下一步"
5. 设置开始时间（建议选择服务器负载较低的时间，如凌晨3点），点击"下一步"
6. 选择"启动程序"，点击"下一步"
7. 在"程序或脚本"框中输入PHP解释器的完整路径，例如：`C:\php\php.exe`
8. 在"添加参数"框中输入auto_backup.php脚本的完整路径，例如：`D:\wwwroot\127.0.0.10\admin\auto_backup.php`
9. 在"起始于"框中输入PHP脚本所在的目录，例如：`D:\wwwroot\127.0.0.10\admin\`
10. 点击"下一步"，然后点击"完成"

### 验证自动备份是否正常工作

1. 右键点击刚创建的任务，选择"运行"
2. 检查`数据库`目录中是否生成了新的备份文件
3. 查看`数据库/backup_log.txt`文件，确认备份过程是否成功

## 备份文件管理

- 系统会自动保留最近10个备份文件，旧的备份文件会被自动删除
- 备份文件保存在网站根目录的`数据库`文件夹中
- 备份文件命名格式为：`database_backup_YYYY-MM-DD_HH-MM-SS.sql`

## 手动恢复数据库

1. 登录管理员账号
2. 进入用户管理页面
3. 点击"恢复数据库"按钮
4. 上传之前导出的SQL备份文件
5. 确认恢复操作

## 注意事项

- 恢复数据库操作会覆盖当前数据库中的所有数据，请谨慎操作
- 建议在执行恢复操作前先导出当前数据库的备份
- 如果自动备份任务失败，请检查PHP路径是否正确，以及PHP是否有足够的权限访问数据库和写入备份文件
- 备份日志保存在`数据库/backup_log.txt`文件中，可以查看此文件了解备份过程的详细信息