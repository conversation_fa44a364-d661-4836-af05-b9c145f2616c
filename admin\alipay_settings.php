<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $appId = trim($_POST['appid']);
    $publicKey = trim($_POST['public_key']);
    $privateKey = trim($_POST['private_key']);
    $isProduction = isset($_POST['is_production']) ? true : false;

    // 验证数据
    $errors = [];
    if (empty($appId)) {
        $errors[] = '应用ID不能为空';
    }
    if (empty($publicKey)) {
        $errors[] = '支付宝公钥不能为空';
    }
    if (empty($privateKey)) {
        $errors[] = '应用私钥不能为空';
    }

    if (empty($errors)) {
        // 更新配置文件
        $configContent = "<?php\n";
        $configContent .= "// 支付宝支付配置\n";
        $configContent .= "define('ALIPAY_APPID', '{$appId}'); // 支付宝应用ID\n";
        $configContent .= "define('ALIPAY_PUBLIC_KEY', '{$publicKey}'); // 支付宝公钥\n";
        $configContent .= "define('ALIPAY_PRIVATE_KEY', '{$privateKey}'); // 应用私钥\n";
        $configContent .= "define('ALIPAY_NOTIFY_URL', SITE_URL . '/alipay_notify.php'); // 支付宝异步通知地址\n";
        $configContent .= "define('ALIPAY_RETURN_URL', SITE_URL . '/alipay_return.php'); // 支付宝同步返回地址\n";
        $configContent .= "define('ALIPAY_GATEWAY', '" . ($isProduction ? "https://openapi.alipay.com/gateway.do" : "https://openapi.alipaydev.com/gateway.do") . "'); // 支付宝网关\n";
        $configContent .= "\n// 支付宝交易状态常量\n";
        $configContent .= "define('ALIPAY_TRADE_SUCCESS', 'TRADE_SUCCESS'); // 支付成功\n";
        $configContent .= "define('ALIPAY_TRADE_FINISHED', 'TRADE_FINISHED'); // 交易完成\n";
        $configContent .= "define('ALIPAY_WAIT_BUYER_PAY', 'WAIT_BUYER_PAY'); // 交易创建，等待买家付款\n";
        $configContent .= "define('ALIPAY_TRADE_CLOSED', 'TRADE_CLOSED'); // 未付款交易超时关闭，或支付完成后全额退款\n";

        if (file_put_contents('../alipay_config.php', $configContent)) {
            setMessage('success', '支付宝配置已成功更新');
            redirect('alipay_settings.php');
        } else {
            $errors[] = '配置文件写入失败，请检查文件权限';
        }
    }
}

// 读取当前配置
$currentConfig = [];
if (file_exists('../alipay_config.php')) {
    $configContent = file_get_contents('../alipay_config.php');
    preg_match("/define\('ALIPAY_APPID', '([^']*)'\)/", $configContent, $matches);
    $currentConfig['appid'] = $matches[1] ?? '';
    
    preg_match("/define\('ALIPAY_PUBLIC_KEY', '([^']*)'\)/", $configContent, $matches);
    $currentConfig['public_key'] = $matches[1] ?? '';
    
    preg_match("/define\('ALIPAY_PRIVATE_KEY', '([^']*)'\)/", $configContent, $matches);
    $currentConfig['private_key'] = $matches[1] ?? '';
    
    $currentConfig['is_production'] = strpos($configContent, 'openapi.alipay.com') !== false;
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝配置 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">支付宝配置</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php foreach ($messages as $message): ?>
                            <div class="alert alert-<?php echo $message['type']; ?>">
                                <?php echo $message['text']; ?>
                            </div>
                        <?php endforeach; ?>

                        <form method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="appid" class="form-label">支付宝应用ID</label>
                                <input type="text" class="form-control" id="appid" name="appid" value="<?php echo htmlspecialchars($currentConfig['appid'] ?? ''); ?>" required>
                                <div class="form-text">在支付宝开放平台获取的应用ID</div>
                            </div>

                            <div class="mb-3">
                                <label for="public_key" class="form-label">支付宝公钥</label>
                                <textarea class="form-control" id="public_key" name="public_key" rows="5" required><?php echo htmlspecialchars($currentConfig['public_key'] ?? ''); ?></textarea>
                                <div class="form-text">用于验证支付宝返回的数据签名</div>
                            </div>

                            <div class="mb-3">
                                <label for="private_key" class="form-label">应用私钥</label>
                                <textarea class="form-control" id="private_key" name="private_key" rows="5" required><?php echo htmlspecialchars($currentConfig['private_key'] ?? ''); ?></textarea>
                                <div class="form-text">用于生成请求签名，请妥善保管</div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_production" name="is_production" <?php echo ($currentConfig['is_production'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_production">生产环境</label>
                                <div class="form-text">勾选表示使用正式环境，不勾选表示使用沙箱环境</div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">保存配置</button>
                                <a href="index.php" class="btn btn-secondary">返回首页</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>