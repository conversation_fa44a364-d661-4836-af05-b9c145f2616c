<?php
// 此文件用于初始化数据库表和基础数据
// 数据库连接配置从config.php获取
require_once 'config.php';

try {
    // 如果是从install.php调用，使用已存在的数据库连接
    global $pdo;
    if (!isset($pdo) || $pdo === null) {
        $pdo = getDbConnection();
    }
    
    // 确保有有效的数据库连接
    if ($pdo === null) {
        throw new PDOException('无法获取数据库连接');
    }
    
    // 创建用户表
    $sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `username` VARCHAR(50) NOT NULL UNIQUE,
        `password` VARCHAR(255) NOT NULL,
        `email` VARCHAR(255) DEFAULT NULL,
        `nickname` VARCHAR(50) DEFAULT NULL,
        `is_admin` TINYINT(1) DEFAULT 0,
        `is_active` TINYINT(1) DEFAULT 1,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建商品分类表
    $sql = "CREATE TABLE IF NOT EXISTS `categories` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `name` VARCHAR(100) NOT NULL,
        `description` TEXT,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `is_active` TINYINT(1) DEFAULT 1
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建商品表
    $sql = "CREATE TABLE IF NOT EXISTS `products` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `category_id` INT NOT NULL,
        `name` VARCHAR(200) NOT NULL,
        `description` TEXT,
        `price` DECIMAL(10,2) NOT NULL,
        `stock` INT NOT NULL DEFAULT 0,
        `image_url` VARCHAR(255),
        `is_active` TINYINT(1) DEFAULT 1,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建购物车表
    $sql = "CREATE TABLE IF NOT EXISTS `cart_items` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `user_id` INT NOT NULL,
        `product_id` INT NOT NULL,
        `quantity` INT NOT NULL DEFAULT 1,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建订单表
    $sql = "CREATE TABLE IF NOT EXISTS `orders` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `user_id` INT NOT NULL,
        `total_amount` DECIMAL(10,2) NOT NULL,
        `status` ENUM('pending', 'paid', 'shipped', 'completed', 'cancelled') DEFAULT 'pending',
        `shipping_address` TEXT NOT NULL,
        `payment_method` ENUM('alipay', 'wechat') NOT NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建订单详情表
    $sql = "CREATE TABLE IF NOT EXISTS `order_items` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `order_id` INT NOT NULL,
        `product_id` INT NOT NULL,
        `quantity` INT NOT NULL,
        `price` DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $pdo->exec($sql);
    
    // 创建默认管理员账号
    // 首先检查users表是否有nickname字段
    $hasNickname = false;
    try {
        $checkStmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'nickname'");
        $checkStmt->execute();
        $hasNickname = $checkStmt->rowCount() > 0;
    } catch (PDOException $e) {
        // 如果出错，假设没有nickname字段
        $hasNickname = false;
    }
    
    // 检查admin用户是否已存在
    $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM `users` WHERE `username` = 'admin'");
    $checkStmt->execute();
    $adminExists = $checkStmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        if ($hasNickname) {
            $sql = "INSERT INTO `users` (`username`, `password`, `is_admin`, `nickname`) 
                   VALUES ('admin', ?, 1, '管理员')";
        } else {
            $sql = "INSERT INTO `users` (`username`, `password`, `is_admin`) 
                   VALUES ('admin', ?, 1)";
        }
        $stmt = $pdo->prepare($sql);
        $stmt->execute([password_hash('admin123', PASSWORD_DEFAULT)]);
    }
    
    // 如果是直接访问此文件，显示成功消息
    if (basename($_SERVER['PHP_SELF']) == 'init_db.php') {
        echo "<div style='text-align:center; margin-top:50px;'>";
        echo "<h2>数据库初始化成功！</h2>";
        echo "<p>所有表和初始数据已创建完成。</p>";
        echo "<p><a href='index.php'>返回首页</a></p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    die("数据库初始化失败: " . $e->getMessage());
}