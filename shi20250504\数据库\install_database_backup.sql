-- 创建数据库
CREATE DATABASE IF NOT EXISTS `jd07` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `jd07`;


-- 删除所有视图

-- 删除所有存储过程

-- 删除所有函数

-- 删除所有事件

-- 删除所有触发器

-- 导出 users 表结构
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `is_admin` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 导出 users 表数据
INSERT INTO `users` (`id`, `username`, `password`, `email`, `nickname`, `is_admin`, `is_active`, `created_at`) VALUES
('1', 'admin', '$2y$10$JSHd0NQwPE6vrt2nl6Xp3uX9RY5.xsrG9DwiikM5yRVeohnQhpYEm', NULL, '管理员', '1', '1', '2025-03-10 19:26:45');


-- 导出 categories 表结构
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

-- 导出 categories 表数据
INSERT INTO `categories` (`id`, `name`, `description`, `created_at`, `is_active`) VALUES
('2', '内衣服饰', '', '2025-03-10 19:27:23', '0'),
('3', '电脑工具', '一些实用的电脑工具', '2025-03-21 19:20:54', '1');


-- 导出 products 表结构
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT '0',
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_top` tinyint(1) NOT NULL DEFAULT '0',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_top_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- 导出 products 表数据
INSERT INTO `products` (`id`, `category_id`, `name`, `description`, `price`, `stock`, `image_url`, `is_active`, `is_top`, `sort_order`, `created_at`, `updated_at`, `last_top_time`) VALUES
('1', '3', '电脑工具', '定时关机、定时休眠、快捷键关机、快捷键休眠。有需要联系QQ573702927，付款。终身受用。', '9.90', '10000', 'uploads/products/product_67dd4b5f1df84.jpg', '1', '1', '1', '2025-03-10 19:27:51', '2025-03-22 16:03:26', '2025-03-22 16:03:26'),
('3', '3', '1212', '1122', '112.00', '12121', 'uploads/products/product_67dd5c26c754b.jpg', '1', '1', '0', '2025-03-21 20:31:34', '2025-03-22 16:05:11', '2025-03-22 16:05:11'),
('4', '3', '333', '6666', '33.00', '33333', 'uploads/products/product_67de1c149db97.jpg', '1', '1', '0', '2025-03-22 10:10:28', '2025-03-22 16:03:15', '2025-03-22 16:03:15');


-- 导出 orders 表结构
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','shipped','completed','cancelled') DEFAULT 'pending',
  `shipping_address` text NOT NULL,
  `payment_method` enum('alipay','wechat') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 导出 orders 表数据
INSERT INTO `orders` (`id`, `user_id`, `total_amount`, `status`, `shipping_address`, `payment_method`, `created_at`, `updated_at`) VALUES
('1', '1', '11.00', 'paid', '111', 'alipay', '2025-03-10 19:28:02', '2025-03-10 19:28:03');


-- 导出 order_items 表结构
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 导出 order_items 表数据
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `price`) VALUES
('1', '1', '1', '1', '11.00');


-- 导出 cart_items 表结构
DROP TABLE IF EXISTS `cart_items`;
CREATE TABLE `cart_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `cart_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `cart_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



-- 导出 product_images 表结构
DROP TABLE IF EXISTS `product_images`;
CREATE TABLE `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `is_primary` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;

-- 导出 product_images 表数据
INSERT INTO `product_images` (`id`, `product_id`, `image_url`, `is_primary`, `created_at`) VALUES
('1', '1', 'uploads/products/product_67dd4b5f1df84.jpg', '0', '2025-03-21 19:19:59'),
('2', '1', 'uploads/products/product_67dd4b5f1e65e.jpg', '0', '2025-03-21 19:19:59'),
('3', '1', 'uploads/products/product_67dd4b5f1e969.jpg', '0', '2025-03-21 19:19:59'),
('7', '3', 'uploads/products/product_67dd5c26c754b.jpg', '1', '2025-03-21 20:31:34'),
('8', '4', 'uploads/products/product_67de1c149db97.jpg', '1', '2025-03-22 10:10:28');

