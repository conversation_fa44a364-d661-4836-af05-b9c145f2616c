<?php
session_start();

// 初始化错误数组
$errors = [];

// 设置默认值
if (!isset($_SESSION['db_config'])) {
    $_SESSION['db_config'] = [
        'host' => 'localhost',
        'port' => '3306',
        'username' => 'root',
        'password' => '111111',
        'database' => 'software_store',
        'site_name' => '软件商城',
        'site_url' => 'http://127.0.0.10',
        'admin_username' => 'admin',
        'admin_password' => 'admin123',
        'admin_email' => ''
    ];
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $_SESSION['db_config'] = [
        'host' => trim($_POST['db_host'] ?? 'localhost'),
        'port' => trim($_POST['db_port'] ?? '3306'),
        'username' => trim($_POST['db_username'] ?? ''),
        'password' => $_POST['db_password'] ?? '',
        'database' => trim($_POST['db_database'] ?? ''),
        'site_name' => trim($_POST['site_name'] ?? '软件商城'),
        'site_url' => trim($_POST['site_url'] ?? 'http://127.0.0.10'),
        'admin_username' => trim($_POST['admin_username'] ?? 'admin'),
        'admin_password' => $_POST['admin_password'] ?? '',
        'admin_email' => trim($_POST['admin_email'] ?? '')
    ];
    
    // 验证必填字段
    if (empty($_SESSION['db_config']['username'])) {
        $errors[] = '数据库用户名不能为空';
    }
    if (empty($_SESSION['db_config']['database'])) {
        $errors[] = '数据库名不能为空';
    }
    if (empty($_SESSION['db_config']['admin_password'])) {
        $errors[] = '管理员密码不能为空';
    }
    if (strlen($_SESSION['db_config']['admin_password']) < 6) {
        $errors[] = '管理员密码至少6位';
    }
    
    if (empty($errors)) {
        header('Location: index.php?step=3');
        exit;
    }
}

$config = $_SESSION['db_config'];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库配置 - 软件商城安装向导</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">
                        <h2><i class="bi bi-database"></i> 数据库配置</h2>
                        <p class="text-muted">请输入数据库连接信息和网站基本配置</p>

                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="post">
                            <!-- 数据库配置 -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-server"></i> 数据库连接信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="mb-3">
                                                <label for="db_host" class="form-label">数据库服务器地址</label>
                                                <input type="text" class="form-control" id="db_host" name="db_host" 
                                                       value="<?php echo htmlspecialchars($config['host']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="db_port" class="form-label">端口</label>
                                                <input type="number" class="form-control" id="db_port" name="db_port" 
                                                       value="<?php echo htmlspecialchars($config['port']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="db_username" class="form-label">数据库用户名 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="db_username" name="db_username" 
                                                       value="<?php echo htmlspecialchars($config['username']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="db_password" class="form-label">数据库密码</label>
                                                <input type="password" class="form-control" id="db_password" name="db_password" 
                                                       value="<?php echo htmlspecialchars($config['password']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="db_database" class="form-label">数据库名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="db_database" name="db_database" 
                                               value="<?php echo htmlspecialchars($config['database']); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 网站配置 -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-globe"></i> 网站基本信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="site_name" class="form-label">网站名称</label>
                                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                                       value="<?php echo htmlspecialchars($config['site_name']); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="site_url" class="form-label">网站地址</label>
                                                <input type="url" class="form-control" id="site_url" name="site_url" 
                                                       value="<?php echo htmlspecialchars($config['site_url']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 管理员账号 -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-person-gear"></i> 管理员账号</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="admin_username" class="form-label">管理员用户名</label>
                                                <input type="text" class="form-control" id="admin_username" name="admin_username" 
                                                       value="<?php echo htmlspecialchars($config['admin_username']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="admin_password" class="form-label">管理员密码 <span class="text-danger">*</span></label>
                                                <input type="password" class="form-control" id="admin_password" name="admin_password" 
                                                       value="<?php echo htmlspecialchars($config['admin_password']); ?>" required minlength="6">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="admin_email" class="form-label">管理员邮箱</label>
                                                <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                                       value="<?php echo htmlspecialchars($config['admin_email']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php?step=1" class="btn btn-secondary btn-lg">
                                    <i class="bi bi-arrow-left"></i> 上一步
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    下一步：测试连接 <i class="bi bi-arrow-right"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
