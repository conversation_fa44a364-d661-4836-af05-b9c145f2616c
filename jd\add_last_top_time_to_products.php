<?php
// 包含配置文件
require_once 'config.php';

try {
    // 获取数据库连接
    $pdo = getDbConnection();
    
    // 读取SQL文件内容
    $sql = file_get_contents(__DIR__ . '/数据库/add_last_top_time_to_products.sql');
    
    // 执行SQL语句
    $result = $pdo->exec($sql);
    
    echo "<p>商品最后置顶时间字段已成功添加！</p>";
    echo "<p><a href='/admin/products.php'>返回商品管理页面</a></p>";
    
} catch (PDOException $e) {
    echo "<p>添加字段失败: " . $e->getMessage() . "</p>";
}
?>