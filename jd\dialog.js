/**
 * 对话框组件
 * 用于创建美观的确认对话框
 */
class Dialog {
    /**
     * 创建一个新的对话框实例
     */
    constructor() {
        this.overlay = null;
        this.container = null;
        this.content = null;
        this.buttons = null;
        this.confirmBtn = null;
        this.cancelBtn = null;
        this.confirmCallback = null;
        this.cancelCallback = null;
    }

    /**
     * 创建对话框DOM元素
     * @param {string} message - 对话框显示的消息
     * @param {string} confirmText - 确认按钮文本
     * @param {string} cancelText - 取消按钮文本
     */
    createDialog(message, confirmText = '确定', cancelText = '取消') {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'dialog-overlay';
        
        // 创建对话框容器
        this.container = document.createElement('div');
        this.container.className = 'dialog-container';
        
        // 创建内容区域
        this.content = document.createElement('div');
        this.content.className = 'dialog-content';
        this.content.textContent = message;
        
        // 创建按钮区域
        this.buttons = document.createElement('div');
        this.buttons.className = 'dialog-buttons';
        
        // 创建确认按钮
        this.confirmBtn = document.createElement('button');
        this.confirmBtn.className = 'dialog-btn dialog-btn-confirm';
        this.confirmBtn.textContent = confirmText;
        
        // 创建取消按钮
        this.cancelBtn = document.createElement('button');
        this.cancelBtn.className = 'dialog-btn dialog-btn-cancel';
        this.cancelBtn.textContent = cancelText;
        
        // 组装DOM结构
        this.buttons.appendChild(this.confirmBtn);
        this.buttons.appendChild(this.cancelBtn);
        this.container.appendChild(this.content);
        this.container.appendChild(this.buttons);
        this.overlay.appendChild(this.container);
        
        // 添加到body
        document.body.appendChild(this.overlay);
        
        // 绑定事件
        this.bindEvents();
        
        // 显示对话框
        setTimeout(() => {
            this.overlay.classList.add('show');
        }, 10);
    }
    
    /**
     * 绑定事件处理
     */
    bindEvents() {
        // 确认按钮点击事件
        this.confirmBtn.addEventListener('click', () => {
            if (typeof this.confirmCallback === 'function') {
                this.confirmCallback();
            }
            this.close();
        });
        
        // 取消按钮点击事件
        this.cancelBtn.addEventListener('click', () => {
            if (typeof this.cancelCallback === 'function') {
                this.cancelCallback();
            }
            this.close();
        });
        
        // 点击遮罩层关闭对话框
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });
        
        // ESC键关闭对话框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.close();
            }
        });
    }
    
    /**
     * 关闭对话框
     */
    close() {
        this.overlay.classList.remove('show');
        setTimeout(() => {
            if (this.overlay && this.overlay.parentNode) {
                document.body.removeChild(this.overlay);
            }
        }, 300);
    }
    
    /**
     * 显示确认对话框
     * @param {string} message - 对话框显示的消息
     * @param {Function} onConfirm - 确认回调函数
     * @param {Function} onCancel - 取消回调函数
     * @param {string} confirmText - 确认按钮文本
     * @param {string} cancelText - 取消按钮文本
     */
    confirm(message, onConfirm, onCancel, confirmText = '确定', cancelText = '取消') {
        this.confirmCallback = onConfirm;
        this.cancelCallback = onCancel;
        this.createDialog(message, confirmText, cancelText);
    }
}

// 创建全局对话框实例
const dialog = new Dialog();

// 示例用法
// dialog.confirm('确定要删除该商品吗？此操作不可恢复！', 
//     () => { console.log('用户点击了确定'); },
//     () => { console.log('用户点击了取消'); }
// );