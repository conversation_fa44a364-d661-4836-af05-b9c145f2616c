// 数据库连接函数
function getDbConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查必要的表是否存在
        $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
        $missingTables = [];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
            if (count($stmt) === 0) {
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables) && basename($_SERVER['PHP_SELF']) !== 'install.php' && basename($_SERVER['PHP_SELF']) !== 'install_wizard.php' && basename($_SERVER['PHP_SELF']) !== 'fix_database.php') {
            // 如果缺少必要的表，重定向到修复数据库页面
            header('Location: fix_database.php');
            exit;
        }
        
        return $pdo;
    } catch (PDOException $e) {
        // 检查是否是因为数据库不存在导致的错误
        if (strpos($e->getMessage(), "Unknown database") !== false) {
            try {
                // 尝试创建数据库连接（不指定数据库名）
                $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 创建数据库
                $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                $pdo->exec($sql);
                
                // 重新尝试连接（包含数据库名）
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
                $pdo = new PDO($dsn, DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 初始化数据库表和数据
                require_once __DIR__ . '/init_db.php';
                
                return $pdo;
            } catch (PDOException $e2) {
                // 如果初始化失败，重定向到安装程序
                // 避免在已经在安装页面时再次重定向
                if (basename($_SERVER['PHP_SELF']) !== 'install.php' && basename($_SERVER['PHP_SELF']) !== 'install_wizard.php') {
                    header('Location: install_wizard.php');
                    exit;
                }
            }
        }
        // 如果连接失败且不在特殊页面中，重定向到修复数据库页面
        if (basename($_SERVER['PHP_SELF']) !== 'install.php' && basename($_SERVER['PHP_SELF']) !== 'install_wizard.php' && basename($_SERVER['PHP_SELF']) !== 'fix_database.php') {
            header('Location: fix_database.php');
            exit;
        }
        
        // 如果已经在安装页面，则返回null而不是抛出异常
        // 这样安装页面可以继续执行安装流程
        return null;
    }
}

// 检查用户是否已登录
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// 检查用户是否是管理员
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1;
}

// 获取当前登录用户ID
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

// 重定向函数
function redirect($url) {
    header("Location: $url");
    exit;
}

// 显示错误消息
function showError($message) {
    $_SESSION['error'] = $message;
}

// 显示成功消息
function showSuccess($message) {
    $_SESSION['success'] = $message;
}

// 获取并清除消息
function getMessages() {
    $messages = [];
    
    if (isset($_SESSION['error'])) {
        $messages[] = [
            'type' => 'danger',
            'text' => $_SESSION['error']
        ];
        unset($_SESSION['error']);
    }
    
    if (isset($_SESSION['success'])) {
        $messages[] = [
            'type' => 'success',
            'text' => $_SESSION['success']
        ];
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['warning'])) {
        $messages[] = [
            'type' => 'warning',
            'text' => $_SESSION['warning']
        ];
        unset($_SESSION['warning']);
    }
    
    if (isset($_SESSION['info'])) {
        $messages[] = [
            'type' => 'info',
            'text' => $_SESSION['info']
        ];
        unset($_SESSION['info']);
    }
    
    return $messages;
}

// 设置消息
function setMessage($type, $message) {
    if ($type === 'error') {
        showError($message);
    } elseif ($type === 'success') {
        showSuccess($message);
    } else {
        $_SESSION[$type] = $message;
    }
}

// 获取购物车商品总数
function getCartItemCount() {
    if (!isLoggedIn()) {
        return 0;
    }
    
    $pdo = getDbConnection();
    $userId = getCurrentUserId();
    
    $stmt = $pdo->prepare("SELECT SUM(quantity) FROM cart_items WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $count = $stmt->fetchColumn();
    
    return (int)$count;
}