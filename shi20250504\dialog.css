/* 对话框遮罩层 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-overlay.show {
    opacity: 1;
}

/* 对话框容器 */
.dialog-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.94));
    border-radius: 24px;
    padding: 32px;
    max-width: 90%;
    width: 400px;
    box-shadow: 
        0 20px 50px rgba(0, 0, 0, 0.18),
        0 3px 15px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.08) inset;
    transform: scale(0.95) translateY(10px);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.12);
    position: relative;
}

.dialog-overlay.show .dialog-container {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* 对话框内容 */
.dialog-content {
    text-align: center;
    margin-bottom: 28px;
    color: #2c3e50;
    font-size: 16.5px;
    line-height: 1.6;
    font-weight: 500;
    padding: 0 12px;
}

/* 按钮容器 */
.dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 18px;
}

/* 按钮样式 */
.dialog-btn {
    padding: 13px 30px;
    border: none;
    border-radius: 14px;
    font-size: 15.5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    min-width: 120px;
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
}

.dialog-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-btn:hover::before {
    opacity: 1;
}

/* 确定按钮 */
.dialog-btn-confirm {
    background: linear-gradient(135deg, #ff7eb3, #ff2c78);
    color: white;
    box-shadow: 
        0 8px 20px rgba(255, 71, 135, 0.28),
        0 2px 4px rgba(255, 71, 135, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    border-radius: 16px;
}

.dialog-btn-confirm:hover {
    background: linear-gradient(135deg, #ff8fc0, #ff3d89);
    transform: translateY(-2px);
    box-shadow: 
        0 12px 25px rgba(255, 71, 135, 0.35),
        0 3px 6px rgba(255, 71, 135, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* 取消按钮 */
.dialog-btn-cancel {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    color: #475569;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(0, 0, 0, 0.02);
}

.dialog-btn-cancel:hover {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    transform: translateY(-2px);
    box-shadow: 
        0 12px 25px rgba(0, 0, 0, 0.12),
        0 3px 6px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(0, 0, 0, 0.03);
    color: #334155;
}

/* 禁用状态 */
.dialog-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}