<?php
// 检查是否已经安装
if (file_exists('config.php')) {
    $config = file_get_contents('config.php');
    if (strpos($config, 'DB_INSTALLED') !== false && !isset($_GET['force_install'])) {
        // 尝试测试数据库连接
        require_once 'config.php';
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查必要的表是否存在
            $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
                if (count($stmt) === 0) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                // 如果缺少必要的表，但配置文件存在，提示用户修复
                $needFix = true;
            } else {
                // 如果连接成功且没有强制安装参数，则重定向到首页
                header('Location: index.php');
                exit;
            }
        } catch (PDOException $e) {
            // 数据库连接失败，继续安装流程
            // 不做任何操作，继续执行安装向导
        }
    }
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$dbHost = $_POST['db_host'] ?? 'localhost';
$dbUser = $_POST['db_user'] ?? '';
$dbPass = $_POST['db_pass'] ?? '';
$dbName = $_POST['db_name'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (empty($dbUser) || empty($dbName)) {
        $error = '请填写所有必填字段';
    } else {
        try {
            // 尝试连接数据库
            $pdo = new PDO("mysql:host={$dbHost}", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查数据库是否已存在
            $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbName}'");
            $dbExists = $stmt->rowCount() > 0;
            
            if ($dbExists) {
                // 尝试使用已存在的数据库
                $pdo->exec("USE `{$dbName}`");
                
                // 检查是否有表存在
                $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
                $existingTables = [];
                
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
                    if (count($stmt) > 0) {
                        $existingTables[] = $table;
                    }
                }
                
                if (!empty($existingTables)) {
                    $error = '数据库已存在且包含表: ' . implode(', ', $existingTables) . '。请使用其他数据库名称或清空此数据库。';
                } else {
                    // 数据库存在但没有表，可以继续使用
                    $dbReady = true;
                }
            } else {
                // 创建新数据库
                $pdo->exec("CREATE DATABASE `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$dbName}`");
                $dbReady = true;
            }
            
            if (isset($dbReady) && $dbReady) {
                // 更新配置文件
                $configContent = file_get_contents('config.php');
                $configContent = preg_replace(
                    ['/define\([\'"]DB_HOST[\'"],\s*[^)]+\);/', 
                     '/define\([\'"]DB_USER[\'"],\s*[^)]+\);/',
                     '/define\([\'"]DB_PASS[\'"],\s*[^)]+\);/',
                     '/define\([\'"]DB_NAME[\'"],\s*[^)]+\);/'],
                    ["define('DB_HOST', '{$dbHost}');",
                     "define('DB_USER', '{$dbUser}');",
                     "define('DB_PASS', '{$dbPass}');",
                     "define('DB_NAME', '{$dbName}');"],
                    $configContent
                );
                
                // 添加安装标记（如果不存在）
                if (strpos($configContent, "define('DB_INSTALLED'") === false) {
                    $configContent = str_replace('session_start();', "define('DB_INSTALLED', true);\nsession_start();", $configContent);
                }
                
                file_put_contents('config.php', $configContent);
                
                // 初始化数据库表和基础数据
                require_once 'init_db.php';
                
                // 设置成功消息并跳转到第三步
                $success = '数据库配置成功！';
                $step = 3;
            }
        } catch (PDOException $e) {
            $error = '数据库连接失败：' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站安装向导</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .install-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }
        .install-steps::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step {
            position: relative;
            z-index: 2;
            background: white;
            padding: 0 1rem;
            text-align: center;
        }
        .step-number {
            width: 50px;
            height: 50px;
            line-height: 46px;
            border: 2px solid #e9ecef;
            border-radius: 50%;
            display: inline-block;
            background: white;
            margin-bottom: 0.5rem;
            font-weight: bold;
            transition: all 0.3s;
        }
        .step.active .step-number {
            border-color: #0d6efd;
            color: #0d6efd;
        }
        .step.completed .step-number {
            background: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="card-title text-center mb-4">网站安装向导</h3>
                        
                        <div class="install-steps">
                            <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">
                                <div class="step-number">1</div>
                                <div class="step-title">环境检测</div>
                            </div>
                            <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">
                                <div class="step-number">2</div>
                                <div class="step-title">数据库配置</div>
                            </div>
                            <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">
                                <div class="step-number">3</div>
                                <div class="step-title">安装完成</div>
                            </div>
                        </div>

                        <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php else: ?>
                        
                        <?php if ($step === 1): ?>
                        <div class="mb-4">
                            <h5>系统环境检测</h5>
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td>PHP版本</td>
                                        <td><?php echo PHP_VERSION; ?></td>
                                        <td><?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? '<span class="text-success">✓</span>' : '<span class="text-danger">✗</span>'; ?></td>
                                    </tr>
                                    <tr>
                                        <td>PDO扩展</td>
                                        <td><?php echo extension_loaded('pdo') ? '已安装' : '未安装'; ?></td>
                                        <td><?php echo extension_loaded('pdo') ? '<span class="text-success">✓</span>' : '<span class="text-danger">✗</span>'; ?></td>
                                    </tr>
                                    <tr>
                                        <td>PDO MySQL扩展</td>
                                        <td><?php echo extension_loaded('pdo_mysql') ? '已安装' : '未安装'; ?></td>
                                        <td><?php echo extension_loaded('pdo_mysql') ? '<span class="text-success">✓</span>' : '<span class="text-danger">✗</span>'; ?></td>
                                    </tr>
                                </tbody>
                            </table>
                            <?php if (version_compare(PHP_VERSION, '7.4.0', '>=') && extension_loaded('pdo') && extension_loaded('pdo_mysql')): ?>
                            <div class="d-grid gap-2">
                                <a href="?step=2" class="btn btn-primary">下一步</a>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-warning">请确保您的系统满足所有要求后再继续安装。</div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($step === 2): ?>
                        <form method="post" action="" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="db_host" class="form-label">数据库主机</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                <div class="form-text">通常为localhost</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_user" class="form-label">数据库用户名</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">数据库密码</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_name" class="form-label">数据库名称</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" required>
                                <div class="form-text">如果数据库不存在，系统会自动创建</div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">开始安装</button>
                                <a href="?step=1" class="btn btn-outline-secondary">返回上一步</a>
                            </div>
                        </form>
                        <?php endif; ?>
                        
                        <?php if ($step === 3): ?>
                        <div class="text-center mb-4">
                            <div class="mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                </svg>
                            </div>
                            <h4 class="mb-3">安装完成！</h4>
                            <p>网站已成功安装并配置完成。</p>
                            <p>您可以使用以下默认管理员账号登录：</p>
                            <div class="card mb-3 mx-auto" style="max-width: 300px;">
                                <div class="card-body text-start">
                                    <p class="mb-1"><strong>用户名：</strong> admin</p>
                                    <p class="mb-0"><strong>密码：</strong> admin123</p>
                                </div>
                            </div>
                            <p class="text-muted small">请登录后立即修改默认密码！</p>
                            <div class="mt-4">
                                <a href="index.php" class="btn btn-primary">进入网站首页</a>
                                <a href="admin/index.php" class="btn btn-outline-secondary ms-2">进入管理后台</a>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>