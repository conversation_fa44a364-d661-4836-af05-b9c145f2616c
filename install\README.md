# 软件商城安装程序

## 概述

这是一个完整的Web安装向导，用于安装软件商城系统。安装程序提供了用户友好的界面，引导用户完成整个安装过程。

## 安装步骤

### 1. 环境检查
- 检查PHP版本（要求 >= 7.4.0）
- 检查必需的PHP扩展（PDO MySQL、GD）
- 检查文件写入权限
- 检查内存限制

### 2. 数据库配置
- 输入数据库服务器信息
- 配置网站基本信息
- 设置管理员账号

### 3. 数据库连接测试
- 测试数据库连接
- 验证权限
- 创建数据库（如果不存在）

### 4. 安装数据库
- 创建数据表结构
- 插入初始数据
- 创建管理员账号

### 5. 完成安装
- 生成配置文件
- 显示安装摘要
- 提供后续操作指引

## 使用方法

1. 将网站文件上传到Web服务器
2. 确保Web服务器对以下目录有写入权限：
   - `uploads/` 目录
   - 根目录（用于创建config.php）
3. 在浏览器中访问 `http://你的域名/install/`
4. 按照安装向导的提示完成安装

## 安全建议

安装完成后，请务必：

1. **删除install目录** - 防止他人重新安装
2. **修改管理员密码** - 使用强密码
3. **设置适当的文件权限** - 限制敏感文件的访问
4. **定期备份数据库** - 防止数据丢失

## 文件说明

- `index.php` - 安装向导主文件
- `step1_requirements.php` - 环境检查
- `step2_database.php` - 数据库配置
- `step3_test.php` - 连接测试
- `step4_install.php` - 数据库安装
- `step5_complete.php` - 完成安装
- `install.sql` - 数据库结构和初始数据
- `database_backup.sql` - 完整数据库备份
- `config_functions.php` - 配置文件函数模板

## 故障排除

### 常见问题

1. **环境检查失败**
   - 确保PHP版本满足要求
   - 安装缺失的PHP扩展
   - 检查文件权限设置

2. **数据库连接失败**
   - 验证数据库服务器地址和端口
   - 检查用户名和密码
   - 确保数据库服务正在运行

3. **权限不足**
   - 确保数据库用户有创建数据库和表的权限
   - 检查Web服务器对文件系统的写入权限

4. **配置文件生成失败**
   - 检查根目录的写入权限
   - 手动创建config.php文件（安装程序会提供内容）

### 手动安装

如果自动安装失败，可以手动执行以下步骤：

1. 手动创建数据库
2. 导入 `install.sql` 文件
3. 根据安装程序提示创建 `config.php` 文件
4. 创建管理员账号

## 技术要求

- **PHP**: >= 7.4.0
- **MySQL**: >= 5.7.0 或 MariaDB >= 10.2.0
- **Web服务器**: Apache、Nginx 或其他支持PHP的服务器
- **PHP扩展**: PDO、PDO_MySQL、GD、Session

## 支持

如果在安装过程中遇到问题，请检查：

1. 服务器错误日志
2. PHP错误日志
3. 数据库错误日志

确保所有要求都满足，并按照安装向导的提示操作。
