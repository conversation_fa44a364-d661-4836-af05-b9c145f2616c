<?php
require_once 'config.php';
require_once 'alipay.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    setMessage('warning', '请先登录');
    redirect('login.php');
}

$userId = getCurrentUserId();
$pdo = getDbConnection();

// 获取订单ID
$orderId = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// 验证订单是否属于当前用户
$stmt = $pdo->prepare("SELECT * FROM orders WHERE id = :id AND user_id = :user_id");
$stmt->execute([
    'id' => $orderId,
    'user_id' => $userId
]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    setMessage('danger', '订单不存在或无权访问');
    redirect('orders.php');
}

// 如果订单已支付，重定向到订单详情页
if ($order['status'] != 'pending') {
    setMessage('info', '该订单已处理');
    redirect('order_detail.php?id=' . $orderId);
}

// 处理支付请求
if (isset($_POST['pay'])) {
    try {
        // 如果是支付宝支付，验证支付状态
        if ($order['payment_method'] == 'alipay') {
            $paymentSuccess = verifyAlipayPayment($orderId);
            if (!$paymentSuccess) {
                setMessage('warning', '支付验证失败，请重试或联系客服');
                redirect('payment.php?order_id=' . $orderId);
            }
        }
        
        // 更新订单状态为已支付
        $stmt = $pdo->prepare("UPDATE orders SET status = 'paid' WHERE id = :id AND user_id = :user_id");
        $stmt->execute([
            'id' => $orderId,
            'user_id' => $userId
        ]);
        
        setMessage('success', '支付成功！感谢您的购买');
        redirect('order_detail.php?id=' . $orderId);
    } catch (PDOException $e) {
        setMessage('danger', '支付处理失败，请稍后重试');
    }
}

// 获取订单项
$stmt = $pdo->prepare("SELECT oi.*, p.name, p.image_url 
                      FROM order_items oi 
                      JOIN products p ON oi.product_id = p.id 
                      WHERE oi.order_id = :order_id");
$stmt->execute(['order_id' => $orderId]);
$orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付订单 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .payment-qrcode {
            max-width: 200px;
            margin: 0 auto;
        }
        .payment-method-icon {
            font-size: 2rem;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <!-- 消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">订单支付</h4>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <h5>订单编号: <?php echo $orderId; ?></h5>
                            <h3 class="text-danger">¥<?php echo number_format($order['total_amount'], 2); ?></h3>
                        </div>
                        
                        <div class="mb-4">
                            <h5>支付方式</h5>
                            <div class="d-flex align-items-center">
                                <?php if ($order['payment_method'] == 'alipay'): ?>
                                    <i class="bi bi-credit-card text-primary payment-method-icon"></i>
                                    <span>支付宝</span>
                                <?php else: ?>
                                    <i class="bi bi-chat-square text-success payment-method-icon"></i>
                                    <span>微信支付</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <p>请使用<?php echo $order['payment_method'] == 'alipay' ? '支付宝' : '微信'; ?>扫描以下二维码完成支付</p>
                            <div class="payment-qrcode border p-3 mb-3">
                                <?php if ($order['payment_method'] == 'alipay'): ?>
                                    <!-- 使用本地支付宝支付二维码图片 -->
                                    <img src="pay/微信支付.png" alt="支付宝支付二维码" class="img-fluid">
                                <?php else: ?>
                                    <!-- 使用本地微信支付二维码图片 -->
                                    <img src="pay/支付宝支付.png" alt="微信支付二维码" class="img-fluid">
                                <?php endif; ?>
                            </div>

                        </div>
                        
                        <form method="post" class="text-center">
                            <button type="submit" name="pay" class="btn btn-success btn-lg">
                                <i class="bi bi-check-circle"></i> 确认支付
                            </button>
                            <a href="orders.php" class="btn btn-outline-secondary btn-lg ms-2">
                                <i class="bi bi-x-circle"></i> 取消支付
                            </a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>