<?php
session_start();

// 强制跳过安装检查
$_GET['force'] = 1;

// 检查是否已安装
$configFile = dirname(__DIR__) . '/config.php';
if (file_exists($configFile) && !isset($_GET['force'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>系统已安装</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; text-align: center; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; }
            .btn { padding: 10px 20px; margin: 10px; text-decoration: none; border-radius: 5px; }
            .btn-primary { background: #007bff; color: white; }
            .btn-danger { background: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>⚠️ 系统已安装</h2>
            <p>检测到系统已经安装完成。</p>
            <p>如果需要重新安装，请备份现有数据。</p>
            <a href="../index.php" class="btn btn-primary">返回网站</a>
            <a href="?force=1" class="btn btn-danger">强制重新安装</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 获取当前步骤
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// 步骤名称
$stepNames = [
    1 => '环境检查',
    2 => '数据库配置', 
    3 => '数据库连接测试',
    4 => '安装数据库',
    5 => '完成安装'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件商城 - 安装向导</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #e4393c, #c81623);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        .step-item {
            display: flex;
            align-items: center;
            margin: 5px;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .step-active {
            background: #007bff;
            color: white;
        }
        .step-completed {
            background: #28a745;
            color: white;
        }
        .step-pending {
            background: #e9ecef;
            color: #6c757d;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .text-center {
            text-align: center;
        }
        .d-flex {
            display: flex;
        }
        .justify-content-between {
            justify-content: space-between;
        }
        .mt-4 {
            margin-top: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <h1>⚙️ 软件商城安装向导</h1>
        <p>欢迎使用软件商城系统，请按照向导完成安装</p>
    </div>

    <!-- 步骤指示器 -->
    <div class="container">
        <div class="step-indicator">
            <?php for ($i = 1; $i <= 5; $i++): ?>
                <div class="step-item">
                    <div class="step-number <?php 
                        if ($i < $step) echo 'step-completed';
                        elseif ($i == $step) echo 'step-active';
                        else echo 'step-pending';
                    ?>">
                        <?php echo $i; ?>
                    </div>
                    <span><?php echo $stepNames[$i]; ?></span>
                </div>
                <?php if ($i < 5): ?>
                    <div style="margin: 0 10px;">→</div>
                <?php endif; ?>
            <?php endfor; ?>
        </div>
    </div>

    <!-- 安装内容 -->
    <div class="container">
        <div class="card">
            <?php
            switch ($step) {
                case 1:
                    include 'step1_requirements.php';
                    break;
                case 2:
                    // 重定向到独立的step2页面
                    echo '<script>window.location.href = "step2.php";</script>';
                    echo '<div class="alert alert-info">正在跳转到数据库配置页面...</div>';
                    break;
                case 3:
                    include 'step3_test.php';
                    break;
                case 4:
                    include 'step4_install.php';
                    break;
                case 5:
                    include 'step5_complete.php';
                    break;
                default:
                    echo '<div class="alert alert-danger">无效的安装步骤</div>';
                    echo '<a href="?step=1" class="btn btn-primary">返回步骤1</a>';
            }
            ?>
        </div>
    </div>
</body>
</html>
