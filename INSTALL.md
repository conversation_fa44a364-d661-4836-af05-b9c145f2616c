# 软件商城安装说明

## 安装前准备

在开始安装软件商城系统之前，请确保您已准备好以下信息：

1. MySQL数据库服务器地址（通常为localhost或127.0.0.1）
2. MySQL数据库用户名和密码
3. 要使用的数据库名称
4. 网站名称和URL

## 安装方法

### 方法一：使用改进版安装向导（推荐）

1. 访问 `improved_install_wizard.php` 文件启动安装向导
2. 按照向导指引完成以下步骤：
   - 选择安装模式（全新安装或从备份恢复）
   - 配置数据库连接信息
   - 设置网站基本信息
   - 完成安装

### 方法二：使用原始安装向导

1. 访问 `install_wizard.php` 文件启动安装向导
2. 按照向导指引完成安装

## 安装模式说明

### 全新安装

选择此模式将执行以下操作：

1. 如果已存在配置文件和数据库，会自动备份当前数据库
2. 创建新的数据库表结构
3. 初始化基本数据
4. 生成新的配置文件

### 从备份恢复

选择此模式将执行以下操作：

1. 使用您选择的备份文件恢复数据库
2. 生成新的配置文件

## 备份文件

系统会自动在 `数据库` 目录下保存数据库备份文件，文件名格式为：

- 安装前自动备份：`install_backup_YYYY-MM-DD_HH-II-SS.sql`
- 手动备份：`database_backup_YYYY-MM-DD_HH-II-SS.sql`

## 默认管理员账号

安装完成后，您可以使用以下默认管理员账号登录系统：

- 用户名：admin
- 密码：admin123

**重要提示：** 出于安全考虑，请在首次登录后立即修改默认密码！

## 常见问题

### 安装失败

如果安装过程中遇到问题，请检查：

1. 数据库连接信息是否正确
2. 数据库用户是否有足够权限创建数据库和表
3. 网站目录是否有写入权限

### 数据库连接错误

如果遇到数据库连接错误，可以尝试：

1. 访问 `fix_database.php` 修复数据库连接
2. 重新运行安装向导

### 缺少表或字段

如果网站运行过程中提示缺少表或字段，可以尝试：

1. 访问 `fix_database.php` 修复数据库结构
2. 如果问题仍然存在，可以尝试重新安装系统

## 技术支持

如果您在安装过程中遇到任何问题，请联系技术支持团队获取帮助。