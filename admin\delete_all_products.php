<?php
require_once '../config.php';

// 设置响应头为 JSON
header('Content-Type: application/json');

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => '无权限执行此操作']);
    exit;
}

try {
    $pdo = getDbConnection();
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 先删除订单项中的相关数据
    $stmt = $pdo->prepare("DELETE FROM order_items WHERE product_id IN (SELECT id FROM products)");
    $stmt->execute();
    
    // 然后删除所有商品
    $stmt = $pdo->prepare("DELETE FROM products");
    $stmt->execute();
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    // 如果发生错误，回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?> 