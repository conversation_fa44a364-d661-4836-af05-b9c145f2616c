<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取搜索参数
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$perPage = 10;

// 处理分类操作
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    $categoryId = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
    
    try {
        if ($action === 'delete' && $categoryId > 0) {
            // 检查是否有商品使用该分类
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = :category_id");
            $stmt->execute(['category_id' => $categoryId]);
            $productCount = $stmt->fetchColumn();
            
            if ($productCount > 0) {
                // 检查分类下的商品是否被订单引用
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM order_items oi 
                                      JOIN products p ON oi.product_id = p.id 
                                      WHERE p.category_id = :category_id");
                $stmt->execute(['category_id' => $categoryId]);
                $orderReferencedCount = $stmt->fetchColumn();
                
                if ($orderReferencedCount > 0) {
                    setMessage('danger', '无法删除该分类，因为该分类下的商品已被订单引用。请先将商品下架而不是删除。');
                    return redirect('categories.php' . ($search ? "?search=$search" : ''));
                }
            }
            
            // 开始事务
            $pdo->beginTransaction();
            
            try {
                if ($productCount > 0) {
                    // 先删除该分类下的所有商品
                    $stmt = $pdo->prepare("DELETE FROM products WHERE category_id = :category_id");
                    $stmt->execute(['category_id' => $categoryId]);
                }
                
                // 删除分类
                $stmt = $pdo->prepare("DELETE FROM categories WHERE id = :id");
                $stmt->execute(['id' => $categoryId]);
                
                // 提交事务
                $pdo->commit();
                
                if ($productCount > 0) {
                    setMessage('success', '分类及其下' . $productCount . '个商品已删除');
                } else {
                    setMessage('success', '分类已删除');
                }
            } catch (PDOException $e) {
                // 回滚事务
                $pdo->rollBack();
                setMessage('danger', '删除失败：' . $e->getMessage());
            }
        }
    } catch (PDOException $e) {
        setMessage('danger', '操作失败：' . $e->getMessage());
    }
    
    redirect('categories.php' . ($search ? "?search=$search" : ''));
}

// 处理添加/编辑分类
if (isset($_POST['save_category'])) {
    $categoryId = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $isActive = isset($_POST['is_active']) ? 1 : 0;
    
    // 验证数据
    if (empty($name)) {
        setMessage('danger', '分类名称不能为空');
    } else {
        try {
            if ($categoryId > 0) {
                // 更新分类
                $stmt = $pdo->prepare("UPDATE categories SET name = :name, description = :description, is_active = :is_active WHERE id = :id");
                $stmt->execute([
                    'name' => $name,
                    'description' => $description,
                    'is_active' => $isActive,
                    'id' => $categoryId
                ]);
                setMessage('success', '分类已更新');
            } else {
                // 添加分类
                $stmt = $pdo->prepare("INSERT INTO categories (name, description, is_active) VALUES (:name, :description, :is_active)");
                $stmt->execute([
                    'name' => $name,
                    'description' => $description,
                    'is_active' => $isActive
                ]);
                setMessage('success', '分类已添加');
            }
        } catch (PDOException $e) {
            setMessage('danger', '保存失败：' . $e->getMessage());
        }
    }
    
    redirect('categories.php');
}

// 获取要编辑的分类
$editCategory = null;
if (isset($_GET['edit']) && (int)$_GET['edit'] > 0) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = :id");
    $stmt->execute(['id' => $editId]);
    $editCategory = $stmt->fetch(PDO::FETCH_ASSOC);
}

// 构建分类查询
$sql = "SELECT SQL_CALC_FOUND_ROWS c.*, 
        (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id) as product_count 
        FROM categories c WHERE 1";
$params = [];

if ($search) {
    $sql .= " AND (c.name LIKE :search OR c.description LIKE :search)";
    $params['search'] = "%$search%";
}

$sql .= " ORDER BY c.name LIMIT :offset, :limit";
$offset = ($page - 1) * $perPage;

// 执行查询
$stmt = $pdo->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取总记录数
$stmt = $pdo->query("SELECT FOUND_ROWS()");
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin-search.css">
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container">
        <!-- 显示消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endforeach; ?>

        <div class="row">
            <!-- 分类列表 -->
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">分类列表</h5>
                        <form class="d-flex" method="get">
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" placeholder="搜索分类..." value="<?php echo htmlspecialchars($search); ?>">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>描述</th>
                                        <th>商品数</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($categories)): ?>
                                        <tr>
                                            <td colspan="6" class="text-center">暂无分类数据</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($categories as $category): ?>
                                            <tr>
                                                <td><?php echo $category['id']; ?></td>
                                                <td><?php echo htmlspecialchars($category['name']); ?></td>
                                                <td><?php echo htmlspecialchars(mb_substr($category['description'], 0, 30)) . (mb_strlen($category['description']) > 30 ? '...' : ''); ?></td>
                                                <td><?php echo $category['product_count']; ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $category['is_active'] ? 'success' : 'danger'; ?>">
                                                        <?php echo $category['is_active'] ? '启用' : '禁用'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="?edit=<?php echo $category['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(<?php echo $category['id']; ?>, '<?php echo addslashes(htmlspecialchars($category['name'])); ?>', <?php echo $category['product_count']; ?>)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                上一页
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $startPage + 4);
                                    if ($endPage - $startPage < 4) {
                                        $startPage = max(1, $endPage - 4);
                                    }
                                    ?>
                                    
                                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                下一页
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑分类表单 -->
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0"><?php echo $editCategory ? '编辑分类' : '添加分类'; ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <?php if ($editCategory): ?>
                                <input type="hidden" name="id" value="<?php echo $editCategory['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">分类名称</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($editCategory['name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">分类描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($editCategory['description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo (!$editCategory || $editCategory['is_active']) ? 'checked' : ''; ?> onchange="updateCategoryStatus(<?php echo $editCategory['id']; ?>, this.checked)">
                                <label class="form-check-label" for="is_active">启用分类</label>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" name="save_category" class="btn btn-primary">
                                    <i class="bi bi-save"></i> <?php echo $editCategory ? '保存修改' : '添加分类'; ?>
                                </button>
                                <?php if ($editCategory): ?>
                                    <a href="categories.php" class="btn btn-outline-secondary mt-2">
                                        <i class="bi bi-x-lg"></i> 取消编辑
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 删除确认对话框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header border-0">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <i class="bi bi-exclamation-triangle text-warning display-3 mb-4 d-block"></i>
                    <h5 class="mb-3">确定要删除该分类吗？</h5>
                    <p class="text-danger mb-0" id="deleteWarningText"></p>
                </div>
                <div class="modal-footer border-0 justify-content-center">
                    <button type="button" class="btn btn-light px-4" data-bs-dismiss="modal">
                        <i class="bi bi-x-lg me-2"></i>取消
                    </button>
                    <button type="button" class="btn btn-danger px-4" id="confirmDeleteBtn">
                        <i class="bi bi-trash me-2"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const deleteWarningText = document.getElementById('deleteWarningText');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        let categoryIdToDelete = null;

        // 确认删除按钮点击事件
        confirmDeleteBtn.addEventListener('click', function() {
            if (categoryIdToDelete !== null) {
                // 创建并提交表单
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';
                
                const categoryIdInput = document.createElement('input');
                categoryIdInput.type = 'hidden';
                categoryIdInput.name = 'category_id';
                categoryIdInput.value = categoryIdToDelete;
                
                form.appendChild(actionInput);
                form.appendChild(categoryIdInput);
                document.body.appendChild(form);
                form.submit();
            }
            deleteModal.hide();
        });

        // 定义confirmDelete函数，供删除按钮调用
        window.confirmDelete = function(categoryId, categoryName, productCount) {
            categoryIdToDelete = categoryId;
            
            if (productCount > 0) {
                deleteWarningText.textContent = `警告：该操作将同时删除该分类下的${productCount}个商品！`;
            } else {
                deleteWarningText.textContent = '';
            }
            
            deleteModal.show();
        };
    });
    </script>
</body>
</html>

<script>
function updateCategoryStatus(categoryId, isActive) {
    // 只有在编辑现有分类时才执行此操作
    if (!categoryId) {
        return; // 如果没有分类ID，说明是新增分类，不执行AJAX请求
    }
    
    // 创建表单数据
    const formData = new FormData();
    formData.append('id', categoryId); // 确保包含分类ID
    formData.append('category_id', categoryId); // 确保包含分类ID
    formData.append('is_active', isActive ? 1 : 0);
    formData.append('save_category', 1);
    
    // 发送AJAX请求
    fetch('categories.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(() => {
        // 刷新页面以显示更新后的状态
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新分类状态失败');
    });
}
}
</script>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>
</body>
</html>