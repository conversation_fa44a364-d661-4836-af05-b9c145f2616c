<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取订单ID
$orderId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// 获取订单信息
$stmt = $pdo->prepare("SELECT o.*, u.username 
                      FROM orders o 
                      JOIN users u ON o.user_id = u.id 
                      WHERE o.id = :id");
$stmt->execute(['id' => $orderId]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    setMessage('danger', '订单不存在');
    redirect('orders.php');
}

// 获取订单商品
$stmt = $pdo->prepare("SELECT oi.*, p.name, p.image_url 
                      FROM order_items oi 
                      JOIN products p ON oi.product_id = p.id 
                      WHERE oi.order_id = :order_id");
$stmt->execute(['order_id' => $orderId]);
$orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理订单状态更新
if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $newStatus = $_POST['status'] ?? '';
    $validStatuses = ['pending', 'paid', 'shipped', 'completed', 'cancelled'];
    
    if (in_array($newStatus, $validStatuses)) {
        try {
            $stmt = $pdo->prepare("UPDATE orders SET status = :status, updated_at = NOW() WHERE id = :id");
            $stmt->execute([
                'status' => $newStatus,
                'id' => $orderId
            ]);
            setMessage('success', '订单状态已更新为：' . getStatusName($newStatus));
            redirect('orders.php?status=' . urlencode($newStatus));
        } catch (PDOException $e) {
            setMessage('danger', '操作失败：' . $e->getMessage());
        }
    } else {
        setMessage('danger', '无效的订单状态');
    }
}

// 获取消息提示
$messages = getMessages();

// 订单状态名称映射
function getStatusName($status) {
    $statusNames = [
        'pending' => '待支付',
        'paid' => '已支付',
        'shipped' => '已发货',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statusNames[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../dialog.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php">管理后台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2"></i> 仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="bi bi-box-seam"></i> 商品管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">
                            <i class="bi bi-tags"></i> 分类管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="orders.php">
                            <i class="bi bi-receipt"></i> 订单管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="bi bi-shop"></i> 返回商城
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php">
                            <i class="bi bi-box-arrow-right"></i> 退出
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>订单详情 #<?php echo $orderId; ?></h1>
            <a href="orders.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回订单列表
            </a>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">订单商品</h5>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>商品</th>
                                    <th>单价</th>
                                    <th>数量</th>
                                    <th>小计</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../<?php echo $item['image_url']; ?>" alt="<?php echo htmlspecialchars($item['name']); ?>" style="width: 50px; height: 50px; object-fit: cover;" class="me-2">
                                                <span><?php echo htmlspecialchars($item['name']); ?></span>
                                            </div>
                                        </td>
                                        <td>¥<?php echo number_format($item['price'], 2); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td class="text-danger">¥<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">订单总额：</td>
                                    <td class="text-danger fw-bold">¥<?php echo number_format($order['total_amount'], 2); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">订单日志</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <i class="bi bi-circle-fill text-primary"></i>
                                <div class="timeline-content">
                                    <h6 class="mb-1">订单创建</h6>
                                    <p class="text-muted small mb-0"><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></p>
                                </div>
                            </div>
                            <?php if ($order['status'] !== 'pending'): ?>
                                <div class="timeline-item">
                                    <i class="bi bi-circle-fill text-success"></i>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">订单状态更新为：<?php echo getStatusName($order['status']); ?></h6>
                                        <p class="text-muted small mb-0"><?php echo isset($order['updated_at']) ? date('Y-m-d H:i:s', strtotime($order['updated_at'])) : date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">订单状态</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $statusClass = [
                            'pending' => 'warning',
                            'paid' => 'info',
                            'shipped' => 'primary',
                            'completed' => 'success',
                            'cancelled' => 'danger'
                        ];
                        $badgeClass = $statusClass[$order['status']] ?? 'secondary';
                        ?>
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-<?php echo $badgeClass; ?> me-2">
                                <?php echo getStatusName($order['status']); ?>
                            </span>
                            <small class="text-muted">最后更新：<?php echo isset($order['updated_at']) ? date('Y-m-d H:i:s', strtotime($order['updated_at'])) : '未更新'; ?></small>
                        </div>

                        <?php if ($order['status'] === 'pending'): ?>
                            <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要将订单标记为已支付吗？', () => this.submit())">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="status" value="paid">
                                <button type="submit" class="btn btn-info btn-sm w-100 mb-2">
                                    <i class="bi bi-credit-card"></i> 标记为已支付
                                </button>
                            </form>
                            <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要取消该订单吗？', () => this.submit())">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" class="btn btn-danger btn-sm w-100">
                                    <i class="bi bi-x-circle"></i> 取消订单
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if ($order['status'] === 'paid'): ?>
                            <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要将订单标记为已发货吗？', () => this.submit())">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="status" value="shipped">
                                <button type="submit" class="btn btn-primary btn-sm w-100">
                                    <i class="bi bi-truck"></i> 标记为已发货
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if ($order['status'] === 'shipped'): ?>
                            <form method="post" onsubmit="event.preventDefault(); dialog.confirm('确定要将订单标记为已完成吗？', () => this.submit())">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="status" value="completed">
                                <button type="submit" class="btn btn-success btn-sm w-100">
                                    <i class="bi bi-check-circle"></i> 标记为已完成
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">用户信息</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-1">
                            <strong>用户名：</strong>
                            <?php echo htmlspecialchars($order['username']); ?>
                        </p>
                        <p class="mb-1">
                            <strong>邮箱：</strong>
                            <?php echo isset($order['email']) ? htmlspecialchars($order['email']) : '未设置'; ?>
                        </p>
                        <p class="mb-0">
                            <strong>下单时间：</strong>
                            <?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../dialog.js"></script>
</body>
</html>