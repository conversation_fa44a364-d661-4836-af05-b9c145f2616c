<?php
/**
 * 安装前数据库备份脚本
 * 此脚本用于在安装程序运行前自动备份当前数据库
 */

// 设置脚本可以长时间运行
set_time_limit(300);

// 引入配置文件
if (file_exists('config.php')) {
    require_once 'config.php';
} else {
    die("配置文件不存在，无法进行备份");
}

// 日志函数
function logMessage($message) {
    echo date('Y-m-d H:i:s') . " - {$message}\n";
}

// 检查是否从命令行运行
$isCli = (php_sapi_name() === 'cli');

// 如果不是从命令行运行，检查是否是管理员
if (!$isCli && (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1)) {
    // 如果是通过安装向导运行，则允许继续
    if (!isset($_GET['from_installer'])) {
        die("只有管理员可以执行此操作");
    }
}

logMessage("开始备份数据库...");

try {
    // 连接到数据库
    $pdo = getDbConnection();
    if (!$pdo) {
        logMessage("数据库连接失败，无法进行备份");
        die("数据库连接失败，无法进行备份");
    }
    
    // 设置备份文件名
    $timestamp = date('Y-m-d_H-i-s');
    $filename = "install_backup_{$timestamp}.sql";
    $backup_dir = __DIR__ . '/数据库';

    // 确保备份目录存在
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
        logMessage("创建备份目录: {$backup_dir}");
    }

    $backup_file = $backup_dir . '/' . $filename;
    $output = '';
    
    // 获取数据库名称
    $dbName = DB_NAME;
    
    logMessage("备份数据库: {$dbName}");
    
    // 添加创建数据库和使用数据库语句
    $output .= "-- 创建数据库\n";
    $output .= "CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
    $output .= "USE `{$dbName}`;\n\n";

    // 获取所有表
    $tables = [];
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }

    logMessage("找到 " . count($tables) . " 个表");

    // 遍历每个表
    foreach ($tables as $table) {
        logMessage("备份表: {$table}");
        
        // 获取表结构
        $output .= "-- 表结构: {$table}\n";
        $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
        
        $createTableResult = $pdo->query("SHOW CREATE TABLE `{$table}`");
        $createTableRow = $createTableResult->fetch(PDO::FETCH_NUM);
        $output .= $createTableRow[1] . ";\n\n";
        
        // 获取表数据
        $output .= "-- 表数据: {$table}\n";
        $rows = $pdo->query("SELECT * FROM `{$table}`");
        $rowCount = $rows->rowCount();
        
        if ($rowCount > 0) {
            $columns = [];
            for ($i = 0; $i < $rows->columnCount(); $i++) {
                $column = $rows->getColumnMeta($i);
                $columns[] = "`{$column['name']}`";
            }
            
            $output .= "INSERT INTO `{$table}` (" . implode(", ", $columns) . ") VALUES\n";
            
            $rowsData = [];
            while ($row = $rows->fetch(PDO::FETCH_NUM)) {
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = "NULL";
                    } else {
                        $values[] = $pdo->quote($value);
                    }
                }
                $rowsData[] = "(" . implode(", ", $values) . ")";
            }
            
            $output .= implode(",\n", $rowsData) . ";\n\n";
        }
    }

    // 写入文件
    file_put_contents($backup_file, $output);
    logMessage("备份完成: {$backup_file}");
    
    // 如果是通过安装向导运行，返回备份文件路径
    if (isset($_GET['from_installer'])) {
        echo json_encode([
            'success' => true,
            'backup_file' => $backup_file,
            'message' => "备份完成: {$backup_file}"
        ]);
    } else {
        echo "备份完成: {$backup_file}\n";
    }
    
} catch (Exception $e) {
    logMessage("备份失败: " . $e->getMessage());
    
    // 如果是通过安装向导运行，返回错误信息
    if (isset($_GET['from_installer'])) {
        echo json_encode([
            'success' => false,
            'message' => "备份失败: " . $e->getMessage()
        ]);
    } else {
        echo "备份失败: " . $e->getMessage() . "\n";
    }
}