<?php
require_once 'config.php';
require_once 'alipay_config.php';

// 接收支付宝同步返回的数据
$getData = $_GET;

// 记录返回数据到日志文件
file_put_contents('alipay_return_log.txt', date('Y-m-d H:i:s') . ' - ' . json_encode($getData) . "\n", FILE_APPEND);

// 在实际应用中，这里应该验证返回数据的真实性
// 1. 验证签名
// 2. 验证返回数据中的app_id是否为本应用的app_id

// 获取返回中的数据
$outTradeNo = isset($getData['out_trade_no']) ? $getData['out_trade_no'] : '';
$tradeNo = isset($getData['trade_no']) ? $getData['trade_no'] : '';

// 如果订单号为空，重定向到订单列表页
if (empty($outTradeNo)) {
    setMessage('warning', '支付返回数据异常，请检查订单状态');
    redirect('orders.php');
}

// 连接数据库
$pdo = getDbConnection();

// 查询订单信息
$stmt = $pdo->prepare("SELECT * FROM orders WHERE id = :id");
$stmt->execute(['id' => $outTradeNo]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

// 如果订单不存在，重定向到订单列表页
if (!$order) {
    setMessage('danger', '订单不存在');
    redirect('orders.php');
}

// 如果订单已经是已支付状态，直接重定向到订单详情页
if ($order['status'] == 'paid') {
    setMessage('success', '订单支付成功');
    redirect('order_detail.php?id=' . $outTradeNo);
}

// 验证支付状态
// 在实际应用中，应该调用支付宝SDK查询支付状态
// 这里简化处理，仅检查是否有trade_no参数返回
if (!empty($tradeNo)) {
    try {
        // 更新订单状态为已支付
        $stmt = $pdo->prepare("UPDATE orders SET status = 'paid' WHERE id = :id");
        $stmt->execute(['id' => $outTradeNo]);
        
        setMessage('success', '支付成功！感谢您的购买');
    } catch (PDOException $e) {
        setMessage('danger', '支付状态更新失败，请联系客服');
    }
} else {
    setMessage('warning', '支付未完成');
}

// 重定向到订单详情页
redirect('order_detail.php?id=' . $outTradeNo);