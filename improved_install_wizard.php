<?php
// 设置脚本可以长时间运行
set_time_limit(300);

// 检查是否已经安装
if (file_exists('config.php')) {
    $config = file_get_contents('config.php');
    if (strpos($config, 'DB_INSTALLED') !== false && !isset($_GET['force_install'])) {
        // 尝试测试数据库连接
        require_once 'config.php';
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查必要的表是否存在
            $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items', 'product_images'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
                if (count($stmt) === 0) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                // 如果缺少必要的表，但配置文件存在，提示用户修复
                $needFix = true;
            } else {
                // 如果连接成功且没有强制安装参数，则重定向到首页
                header('Location: index.php');
                exit;
            }
        } catch (PDOException $e) {
            // 数据库连接失败，继续安装流程
            // 不做任何操作，继续执行安装向导
        }
    }
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$dbHost = $_POST['db_host'] ?? 'localhost';
$dbUser = $_POST['db_user'] ?? '';
$dbPass = $_POST['db_pass'] ?? '';
$dbName = $_POST['db_name'] ?? '';
$siteName = $_POST['site_name'] ?? '软件商城';
$siteUrl = $_POST['site_url'] ?? 'http://127.0.0.1:8000';
$installMode = $_POST['install_mode'] ?? 'new';
$backupFile = $_POST['backup_file'] ?? '';

// 获取可用的备份文件
function getAvailableBackups() {
    $backup_dir = __DIR__ . '/数据库';
    if (!file_exists($backup_dir)) {
        return [];
    }
    
    $files = glob($backup_dir . '/*.sql');
    usort($files, function($a, $b) {
        return filemtime($b) - filemtime($a); // 按时间降序排列
    });
    
    $result = [];
    foreach ($files as $file) {
        $result[] = [
            'name' => basename($file),
            'path' => $file,
            'size' => formatFileSize(filesize($file)),
            'time' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    
    return $result;
}

// 格式化文件大小
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

// 创建数据库备份
function createDatabaseBackup($pdo, $filename = null) {
    // 设置导出文件名
    if ($filename === null) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "database_backup_{$timestamp}.sql";
    }
    
    $backup_dir = __DIR__ . '/数据库';

    // 确保备份目录存在
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
    }

    $backup_file = $backup_dir . '/' . $filename;
    $output = '';
    
    // 获取数据库名称
    $dbName = DB_NAME;
    
    // 添加创建数据库和使用数据库语句
    $output .= "-- 创建数据库\n";
    $output .= "CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
    $output .= "USE `{$dbName}`;\n\n";

    // 获取所有表
    $tables = [];
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }

    // 遍历每个表
    foreach ($tables as $table) {
        // 获取表结构
        $output .= "-- 表结构: {$table}\n";
        $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
        
        $createTableResult = $pdo->query("SHOW CREATE TABLE `{$table}`");
        $createTableRow = $createTableResult->fetch(PDO::FETCH_NUM);
        $output .= $createTableRow[1] . ";\n\n";
        
        // 获取表数据
        $output .= "-- 表数据: {$table}\n";
        $rows = $pdo->query("SELECT * FROM `{$table}`");
        $rowCount = $rows->rowCount();
        
        if ($rowCount > 0) {
            $columns = [];
            for ($i = 0; $i < $rows->columnCount(); $i++) {
                $column = $rows->getColumnMeta($i);
                $columns[] = "`{$column['name']}`";
            }
            
            $output .= "INSERT INTO `{$table}` (" . implode(", ", $columns) . ") VALUES\n";
            
            $rowsData = [];
            while ($row = $rows->fetch(PDO::FETCH_NUM)) {
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = "NULL";
                    } else {
                        $values[] = $pdo->quote($value);
                    }
                }
                $rowsData[] = "(" . implode(", ", $values) . ")";
            }
            
            $output .= implode(",\n", $rowsData) . ";\n\n";
        }
    }

    // 写入文件
    file_put_contents($backup_file, $output);
    return $backup_file;
}

// 恢复数据库备份
function restoreDatabaseBackup($pdo, $backupFile) {
    // 读取SQL文件内容
    $sqlContent = file_get_contents($backupFile);
    
    // 分割SQL语句
    $sqlStatements = explode(";", $sqlContent);
    
    // 执行每个SQL语句
    foreach ($sqlStatements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // 忽略某些错误，如表已存在等
                if (strpos($e->getMessage(), '1050') === false) { // 1050是表已存在的错误码
                    throw $e;
                }
            }
        }
    }
    
    return true;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 2) {
        // 验证数据库连接
        if (empty($dbUser) || empty($dbName)) {
            $error = '请填写所有必填字段';
        } else {
            try {
                // 尝试连接到数据库
                $dsn = "mysql:host={$dbHost};charset=utf8mb4";
                $pdo = new PDO($dsn, $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 检查数据库是否存在
                $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$dbName}'");
                $dbExists = $stmt->rowCount() > 0;
                
                if (!$dbExists) {
                    // 创建数据库
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                }
                
                // 选择数据库
                $pdo->exec("USE `{$dbName}`");
                
                // 保存配置信息到会话
                $_SESSION['install_db_host'] = $dbHost;
                $_SESSION['install_db_user'] = $dbUser;
                $_SESSION['install_db_pass'] = $dbPass;
                $_SESSION['install_db_name'] = $dbName;
                $_SESSION['install_site_name'] = $siteName;
                $_SESSION['install_site_url'] = $siteUrl;
                $_SESSION['install_mode'] = $installMode;
                $_SESSION['install_backup_file'] = $backupFile;
                
                // 进入下一步
                header("Location: improved_install_wizard.php?step=3");
                exit;
                
            } catch (PDOException $e) {
                $error = '数据库连接失败: ' . $e->getMessage();
            }
        }
    } elseif ($step === 3) {
        // 安装数据库
        try {
            // 从会话中获取配置信息
            $dbHost = $_SESSION['install_db_host'];
            $dbUser = $_SESSION['install_db_user'];
            $dbPass = $_SESSION['install_db_pass'];
            $dbName = $_SESSION['install_db_name'];
            $siteName = $_SESSION['install_site_name'];
            $siteUrl = $_SESSION['install_site_url'];
            $installMode = $_SESSION['install_mode'];
            $backupFile = $_SESSION['install_backup_file'];
            
            // 如果是全新安装且存在配置文件，先备份当前数据库
            if ($installMode === 'new' && file_exists('config.php')) {
                // 执行备份脚本
                include 'install_backup.php';
            }
            
            // 连接到数据库
            $dsn = "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            if ($installMode === 'new') {
                // 新安装：初始化数据库表
                require_once 'init_db.php';
            } else {
                // 从备份恢复：恢复数据库备份
                restoreDatabaseBackup($pdo, $backupFile);
            }
            
            // 创建配置文件
            $configContent = file_get_contents('install_config_template.php');
            $configContent = str_replace('{{DB_HOST}}', $dbHost, $configContent);
            $configContent = str_replace('{{DB_USER}}', $dbUser, $configContent);
            $configContent = str_replace('{{DB_PASS}}', $dbPass, $configContent);
            $configContent = str_replace('{{DB_NAME}}', $dbName, $configContent);
            $configContent = str_replace('{{SITE_NAME}}', $siteName, $configContent);
            $configContent = str_replace('{{SITE_URL}}', $siteUrl, $configContent);
            
            file_put_contents('config.php', $configContent);
            
            // 安装完成，进入下一步
            header("Location: improved_install_wizard.php?step=4");
            exit;
            
        } catch (PDOException $e) {
            $error = '数据库安装失败: ' . $e->getMessage();
        }
    }
}

// 获取可用的备份文件
$availableBackups = getAvailableBackups();

// HTML 头部
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件商城安装向导</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .install-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .step-indicator {
            display: flex;
            margin-bottom: 30px;
            justify-content: space-between;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            position: relative;
        }
        .step.active {
            font-weight: bold;
            color: #007bff;
        }
        .step:not(:last-child):after {
            content: '';
            position: absolute;
            top: 50%;
            right: -10%;
            width: 20%;
            height: 2px;
            background-color: #dee2e6;
        }
        .step.completed:not(:last-child):after {
            background-color: #28a745;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            margin-right: 5px;
        }
        .step.active .step-number {
            background-color: #007bff;
            color: white;
        }
        .step.completed .step-number {
            background-color: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <h1 class="text-center mb-4">软件商城安装向导</h1>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'completed' : ''; ?>">
                    <span class="step-number">1</span> 欢迎
                </div>
                <div class="step <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'completed' : ''; ?>">
                    <span class="step-number">2</span> 数据库配置
                </div>
                <div class="step <?php echo $step >= 3 ? 'active' : ''; ?> <?php echo $step > 3 ? 'completed' : ''; ?>">
                    <span class="step-number">3</span> 安装
                </div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">
                    <span class="step-number">4</span> 完成
                </div>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <?php if (isset($needFix) && $needFix): ?>
                <div class="alert alert-warning">
                    <p>检测到数据库配置已存在，但缺少必要的表。您可以：</p>
                    <a href="fix_database.php" class="btn btn-warning">修复数据库</a>
                    <a href="improved_install_wizard.php?force_install=1" class="btn btn-danger">重新安装</a>
                    <a href="index.php" class="btn btn-secondary">返回首页</a>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- 步骤1：欢迎页面 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h2 class="card-title">欢迎使用软件商城安装向导</h2>
                        <p>本向导将帮助您安装软件商城系统。在开始之前，请确保您已准备好以下信息：</p>
                        <ul>
                            <li>MySQL数据库服务器地址</li>
                            <li>MySQL数据库用户名和密码</li>
                            <li>要使用的数据库名称</li>
                            <li>网站名称和URL</li>
                        </ul>
                        <p>如果您已经有备份文件，也可以选择从备份恢复安装。</p>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="improved_install_wizard.php?step=2" class="btn btn-primary btn-lg">开始安装</a>
                </div>
                
            <?php elseif ($step == 2): ?>
                <!-- 步骤2：数据库配置 -->
                <form method="post" action="improved_install_wizard.php?step=2">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3>安装模式</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="install_mode" id="mode_new" value="new" <?php echo ($installMode === 'new') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="mode_new">
                                    全新安装
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="install_mode" id="mode_restore" value="restore" <?php echo ($installMode === 'restore') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="mode_restore">
                                    从备份恢复
                                </label>
                            </div>
                            
                            <div id="backup_file_section" class="mt-3" style="display: <?php echo ($installMode === 'restore') ? 'block' : 'none'; ?>">
                                <label for="backup_file" class="form-label">选择备份文件</label>
                                <select class="form-select" name="backup_file" id="backup_file">
                                    <?php foreach ($availableBackups as $backup): ?>
                                        <option value="<?php echo htmlspecialchars($backup['path']); ?>">
                                            <?php echo htmlspecialchars($backup['name']); ?> (<?php echo $backup['size']; ?>, <?php echo $backup['time']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (empty($availableBackups)): ?>
                                    <div class="alert alert-warning mt-2">没有找到可用的备份文件。请确保备份文件存放在 /数据库 目录中。</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3>数据库配置</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">数据库服务器</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbHost); ?>" required>
                                <div class="form-text">通常为 localhost 或 127.0.0.1</div>
                            </div>
                            <div class="mb-3">
                                <label for="db_user" class="form-label">数据库用户名</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbUser); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">数据库密码</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($dbPass); ?>">
                            </div>
                            <div class="mb-3">
                                <label for="db_name" class="form-label">数据库名称</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbName); ?>" required>
                                <div class="form-text">如果数据库不存在，系统将尝试创建它</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3>网站配置</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="site_name" class="form-label">网站名称</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="site_url" class="form-label">网站URL</label>
                                <input type="text" class="form-control" id="site_url" name="site_url" value="<?php echo htmlspecialchars($siteUrl); ?>" required>
                                <div class="form-text">例如：http://127.0.0.1:8000 或 http://example.com</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="improved_install_wizard.php?step=1" class="btn btn-secondary">上一步</a>
                        <button type="submit" class="btn btn-primary">下一步</button>
                    </div>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- 步骤3：安装 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3>正在安装</h3>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-4">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <p>正在安装软件商城系统，请稍候...</p>
                        <div id="install-log" class="mt-3">
                            <p>正在连接到数据库...</p>
                        </div>
                    </div>
                </div>
                
                <form method="post" action="improved_install_wizard.php?step=3" id="install-form">
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">开始安装</button>
                    </div>
                </form>
                
                <script>
                    // 自动提交表单开始安装
                    document.addEventListener('DOMContentLoaded', function() {
                        document.getElementById('install-form').submit();
                    });
                </script>
                
            <?php elseif ($step == 4): ?>
                <!-- 步骤4：完成 -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h3>安装完成</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h4>恭喜！软件商城系统已成功安装。</h4>
                        </div>
                        <p>您现在可以访问您的网站并开始使用了。</p>
                        <p>默认管理员账号：</p>
                        <ul>
                            <li>用户名：admin</li>
                            <li>密码：admin123</li>
                        </ul>
                        <p class="text-danger">请尽快登录并修改默认密码！</p>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="index.php" class="btn btn-primary btn-lg">进入网站</a>
                    <a href="admin/index.php" class="btn btn-success btn-lg">进入管理后台</a>
                </div>
            <?php endif; ?>
            
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 安装模式切换
        document.addEventListener('DOMContentLoaded', function() {
            const modeNew = document.getElementById('mode_new');
            const modeRestore = document.getElementById('mode_restore');
            const backupSection = document.getElementById('backup_file_section');
            
            if (modeNew && modeRestore && backupSection) {
                modeNew.addEventListener('change', function() {
                    backupSection.style.display = 'none';
                });
                
                modeRestore.addEventListener('change', function() {
                    backupSection.style.display = 'block';
                });
            }
        });
    </script>
</body>
</html>