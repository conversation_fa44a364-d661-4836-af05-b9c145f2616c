<?php
require_once 'config.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    setMessage('warning', '请先登录');
    redirect('login.php');
}

$userId = getCurrentUserId();
$pdo = getDbConnection();

// 更新购物车商品数量
if (isset($_POST['update_cart'])) {
    foreach ($_POST['quantity'] as $cartItemId => $quantity) {
        $quantity = (int)$quantity;
        if ($quantity > 0) {
            // 检查库存
            $stmt = $pdo->prepare("SELECT p.stock FROM cart_items c 
                                  JOIN products p ON c.product_id = p.id 
                                  WHERE c.id = :cart_item_id AND c.user_id = :user_id");
            $stmt->execute([
                'cart_item_id' => $cartItemId,
                'user_id' => $userId
            ]);
            $stock = $stmt->fetchColumn();
            
            if ($stock >= $quantity) {
                $stmt = $pdo->prepare("UPDATE cart_items SET quantity = :quantity 
                                      WHERE id = :id AND user_id = :user_id");
                $stmt->execute([
                    'quantity' => $quantity,
                    'id' => $cartItemId,
                    'user_id' => $userId
                ]);
            } else {
                setMessage('warning', '部分商品库存不足，已调整为最大可购买数量');
                $stmt = $pdo->prepare("UPDATE cart_items SET quantity = :quantity 
                                      WHERE id = :id AND user_id = :user_id");
                $stmt->execute([
                    'quantity' => $stock,
                    'id' => $cartItemId,
                    'user_id' => $userId
                ]);
            }
        }
    }
    setMessage('success', '购物车已更新');
    redirect('cart.php');
}

// 删除购物车商品
if (isset($_GET['remove']) && is_numeric($_GET['remove'])) {
    $stmt = $pdo->prepare("DELETE FROM cart_items WHERE id = :id AND user_id = :user_id");
    $stmt->execute([
        'id' => $_GET['remove'],
        'user_id' => $userId
    ]);
    setMessage('success', '商品已从购物车中移除');
    redirect('cart.php');
}

// 清空购物车
if (isset($_GET['clear'])) {
    $stmt = $pdo->prepare("DELETE FROM cart_items WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    setMessage('success', '购物车已清空');
    redirect('cart.php');
}

// 获取购物车商品
$stmt = $pdo->prepare("SELECT c.id, c.quantity, p.id as product_id, p.name, p.price, p.stock, p.image_url 
                      FROM cart_items c 
                      JOIN products p ON c.product_id = p.id 
                      WHERE c.user_id = :user_id");
$stmt->execute(['user_id' => $userId]);
$cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 计算总价
$totalPrice = 0;
foreach ($cartItems as $item) {
    $totalPrice += $item['price'] * $item['quantity'];
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .cart-img {
            max-width: 80px;
            max-height: 80px;
            object-fit: cover;
        }
        .quantity-input {
            width: 80px;
        }
        .modal-confirm {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
        }
        .modal-confirm-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            min-width: 300px;
        }
        .modal-confirm h3 {
            margin-bottom: 1.5rem;
            color: #333;
        }
        .modal-confirm .btn-group {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        .modal-confirm .btn {
            min-width: 100px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link active" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <!-- 消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <h1 class="mb-4">我的购物车</h1>

        <?php if (empty($cartItems)): ?>
            <div class="alert alert-info">您的购物车是空的，<a href="index.php">去购物</a></div>
        <?php else: ?>
            <form method="post">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cartItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo !empty($item['image_url']) ? htmlspecialchars($item['image_url']) : 'uploads/default-product.jpg'; ?>" 
                                                 class="cart-img me-3" alt="<?php echo htmlspecialchars($item['name']); ?>">
                                            <a href="product.php?id=<?php echo $item['product_id']; ?>">
                                                <?php echo htmlspecialchars($item['name']); ?>
                                            </a>
                                        </div>
                                    </td>
                                    <td>¥<?php echo number_format($item['price'], 2); ?></td>
                                    <td>
                                        <input type="number" name="quantity[<?php echo $item['id']; ?>]" 
                                               class="form-control quantity-input" value="<?php echo $item['quantity']; ?>" 
                                               min="1" max="<?php echo $item['stock']; ?>">
                                    </td>
                                    <td>¥<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                    <td>
                                        <a href="javascript:void(0)" class="btn btn-sm btn-danger" 
                                           onclick="showDeleteConfirm(<?php echo $item['id']; ?>)">
                                            <i class="bi bi-trash"></i> 删除
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end fw-bold">总计：</td>
                                <td class="fw-bold text-danger">¥<?php echo number_format($totalPrice, 2); ?></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="d-flex justify-content-between mt-3">
                    <div>
                        <a href="javascript:void(0)" class="btn btn-outline-danger" onclick="showClearCartConfirm()">
                            <i class="bi bi-trash"></i> 清空购物车
                        </a>
                        <a href="index.php" class="btn btn-outline-primary ms-2">
                            <i class="bi bi-arrow-left"></i> 继续购物
                        </a>
                    </div>
                    <div>
                        <button type="submit" name="update_cart" class="btn btn-outline-success me-2">
                            <i class="bi bi-arrow-repeat"></i> 更新购物车
                        </button>
                        <a href="checkout.php" class="btn btn-primary">
                            <i class="bi bi-credit-card"></i> 去结算
                        </a>
                    </div>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <!-- 确认删除对话框 -->
    <div class="modal-confirm" id="deleteConfirmModal">
        <div class="modal-confirm-content">
            <h3>确认删除</h3>
            <p>确定要从购物车中移除此商品吗？</p>
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="hideDeleteConfirm()">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 清空购物车确认对话框 -->
    <div class="modal-confirm" id="clearCartConfirmModal">
        <div class="modal-confirm-content">
            <h3>确认清空</h3>
            <p>确定要清空购物车吗？此操作不可恢复。</p>
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="hideClearCartConfirm()">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmClearCart()">确认清空</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    let deleteItemId = null;

    function showDeleteConfirm(itemId) {
        deleteItemId = itemId;
        document.getElementById('deleteConfirmModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    function hideDeleteConfirm() {
        document.getElementById('deleteConfirmModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        deleteItemId = null;
    }

    function confirmDelete() {
        if (deleteItemId) {
            window.location.href = 'cart.php?remove=' + deleteItemId;
        }
    }

    function showClearCartConfirm() {
        document.getElementById('clearCartConfirmModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    function hideClearCartConfirm() {
        document.getElementById('clearCartConfirmModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function confirmClearCart() {
        window.location.href = 'cart.php?clear=1';
    }
    </script>
</body>
</html>