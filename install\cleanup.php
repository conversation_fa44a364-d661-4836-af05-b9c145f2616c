<?php
// 安装后清理脚本
// 此脚本用于删除安装文件，提高安全性

// 检查是否已安装
if (!file_exists('../config.php')) {
    die('系统尚未安装，无法执行清理操作。');
}

require_once '../config.php';

if (!defined('DB_INSTALLED') || DB_INSTALLED !== true) {
    die('系统尚未安装，无法执行清理操作。');
}

// 检查是否是管理员
if (!isLoggedIn() || !isAdmin()) {
    die('只有管理员可以执行清理操作。请先登录管理员账号。');
}

$cleaned = false;
$errors = [];

if (isset($_POST['confirm_cleanup'])) {
    try {
        // 要删除的文件列表
        $filesToDelete = [
            'index.php',
            'step1_requirements.php',
            'step2_database.php',
            'step3_test.php',
            'step4_install.php',
            'step5_complete.php',
            'config_functions.php',
            'install.sql',
            'database_backup.sql',
            'README.md',
            'cleanup.php' // 最后删除自己
        ];
        
        $deletedFiles = [];
        $failedFiles = [];
        
        foreach ($filesToDelete as $file) {
            $filePath = __DIR__ . '/' . $file;
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $deletedFiles[] = $file;
                } else {
                    $failedFiles[] = $file;
                }
            }
        }
        
        // 尝试删除install目录
        if (empty($failedFiles)) {
            if (rmdir(__DIR__)) {
                $cleaned = true;
            } else {
                $errors[] = '无法删除install目录，可能目录不为空';
            }
        } else {
            $errors[] = '以下文件删除失败: ' . implode(', ', $failedFiles);
        }
        
    } catch (Exception $e) {
        $errors[] = '清理过程中发生错误: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理安装文件 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <?php if ($cleaned): ?>
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0"><i class="bi bi-check-circle"></i> 清理完成</h4>
                        </div>
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                            <h5 class="mt-3">安装文件已成功删除</h5>
                            <p class="text-muted">install目录和所有安装文件已被删除，系统安全性得到提升。</p>
                            <div class="mt-4">
                                <a href="../index.php" class="btn btn-primary me-2">
                                    <i class="bi bi-house"></i> 返回首页
                                </a>
                                <a href="../admin/" class="btn btn-success">
                                    <i class="bi bi-gear"></i> 管理后台
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-header bg-warning">
                            <h4 class="mb-0"><i class="bi bi-exclamation-triangle"></i> 清理安装文件</h4>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <h6><i class="bi bi-exclamation-triangle"></i> 清理失败</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle"></i> 重要提醒</h6>
                                <p>此操作将永久删除所有安装文件和install目录，包括：</p>
                                <ul>
                                    <li>安装向导文件</li>
                                    <li>数据库备份文件</li>
                                    <li>安装说明文档</li>
                                    <li>整个install目录</li>
                                </ul>
                                <p class="mb-0"><strong>此操作不可逆转，请确认您已完成安装并且系统运行正常。</strong></p>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> 为什么要删除安装文件？</h6>
                                <p class="mb-0">删除安装文件可以防止他人重新安装系统，避免数据丢失和安全风险。这是安装完成后的重要安全措施。</p>
                            </div>
                            
                            <form method="post" class="mt-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirm" required>
                                    <label class="form-check-label" for="confirm">
                                        我确认已完成安装，同意删除所有安装文件
                                    </label>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <a href="../admin/" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left"></i> 返回管理后台
                                    </a>
                                    <button type="submit" name="confirm_cleanup" class="btn btn-danger">
                                        <i class="bi bi-trash"></i> 确认删除安装文件
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
