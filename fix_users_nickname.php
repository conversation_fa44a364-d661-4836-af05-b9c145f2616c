<?php
require_once 'config.php';

// 检查数据库中是否存在nickname字段，如果不存在则添加
try {
    $pdo = getDbConnection();
    
    // 检查users表中是否已有nickname字段
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'nickname'");
    $stmt->execute();
    $nicknameExists = $stmt->rowCount() > 0;
    
    if (!$nicknameExists) {
        // 添加nickname字段
        $sql = "ALTER TABLE users ADD COLUMN nickname VARCHAR(50) NULL AFTER username";
        $pdo->exec($sql);
        
        // 将所有现有用户的nickname设置为与username相同
        $sql = "UPDATE users SET nickname = username WHERE nickname IS NULL";
        $pdo->exec($sql);
        
        echo "成功添加用户昵称字段并初始化数据。<br>";
    } else {
        echo "用户昵称字段已存在，无需修改。<br>";
    }
    
    echo "<a href='index.php'>返回首页</a>";
    
} catch (PDOException $e) {
    die("数据库操作失败: " . $e->getMessage());
}