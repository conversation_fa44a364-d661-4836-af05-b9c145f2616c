<?php
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // 检查products表是否存在is_top字段
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'is_top'");
    $isTopExists = $stmt->fetch();
    
    // 检查products表是否存在sort_order字段
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'sort_order'");
    $sortOrderExists = $stmt->fetch();
    
    echo "<h2>检查products表字段</h2>";
    
    if ($isTopExists) {
        echo "<p>is_top字段存在</p>";
        
        // 查询置顶商品数量
        $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE is_top = 1");
        $topCount = $stmt->fetchColumn();
        echo "<p>置顶商品数量: {$topCount}</p>";
        
        // 查询置顶商品
        $stmt = $pdo->query("SELECT id, name, is_top, sort_order FROM products WHERE is_top = 1 ORDER BY sort_order ASC");
        $topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($topProducts) > 0) {
            echo "<h3>置顶商品列表</h3>";
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>名称</th><th>是否置顶</th><th>排序值</th></tr>";
            
            foreach ($topProducts as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['name']}</td>";
                echo "<td>{$product['is_top']}</td>";
                echo "<td>{$product['sort_order']}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>没有置顶商品</p>";
        }
    } else {
        echo "<p>is_top字段不存在</p>";
    }
    
    if ($sortOrderExists) {
        echo "<p>sort_order字段存在</p>";
    } else {
        echo "<p>sort_order字段不存在</p>";
    }
    
    echo "<p><a href='/admin/products.php'>返回商品管理页面</a></p>";
    
} catch(PDOException $e) {
    die("数据库查询失败: " . $e->getMessage());
}
?>