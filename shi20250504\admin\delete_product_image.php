<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

// 获取图片ID和商品ID
$imageId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$productId = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 0;

if ($imageId <= 0 || $productId <= 0) {
    setMessage('danger', '参数错误');
    redirect('products.php');
}

try {
    $pdo = getDbConnection();
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 获取图片信息
    $stmt = $pdo->prepare("SELECT * FROM product_images WHERE id = :id AND product_id = :product_id");
    $stmt->execute([
        'id' => $imageId,
        'product_id' => $productId
    ]);
    $image = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$image) {
        setMessage('danger', '图片不存在');
        redirect('product_form.php?id=' . $productId);
    }
    
    // 检查是否是主图
    if ($image['is_primary'] == 1) {
        // 如果是主图，需要更新products表中的image_url
        // 查找是否有其他图片可以设为主图
        $stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = :product_id AND id != :id ORDER BY created_at ASC LIMIT 1");
        $stmt->execute([
            'product_id' => $productId,
            'id' => $imageId
        ]);
        $newPrimaryImage = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($newPrimaryImage) {
            // 将另一张图片设为主图
            $stmt = $pdo->prepare("UPDATE product_images SET is_primary = 1 WHERE id = :id");
            $stmt->execute(['id' => $newPrimaryImage['id']]);
            
            // 更新products表中的image_url
            $stmt = $pdo->prepare("UPDATE products SET image_url = :image_url WHERE id = :id");
            $stmt->execute([
                'image_url' => $newPrimaryImage['image_url'],
                'id' => $productId
            ]);
        } else {
            // 没有其他图片，将products表中的image_url设为空
            $stmt = $pdo->prepare("UPDATE products SET image_url = '' WHERE id = :id");
            $stmt->execute(['id' => $productId]);
        }
    }
    
    // 删除图片文件
    $imagePath = '../' . $image['image_url'];
    if (file_exists($imagePath)) {
        unlink($imagePath);
    }
    
    // 从数据库中删除图片记录
    $stmt = $pdo->prepare("DELETE FROM product_images WHERE id = :id");
    $stmt->execute(['id' => $imageId]);
    
    // 提交事务
    $pdo->commit();
    
    setMessage('success', '图片已成功删除');
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    setMessage('danger', '删除失败：' . $e->getMessage());
}

// 重定向回商品编辑页面
redirect('product_form.php?id=' . $productId);