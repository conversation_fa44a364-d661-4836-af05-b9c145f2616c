<?php
// 检查是否完成了数据库安装
if (!isset($_SESSION['install_success']) || !isset($_SESSION['db_config'])) {
    echo '<script>window.location.href = "?step=1";</script>';
    echo '<div class="alert alert-warning">请先完成前面的安装步骤...</div>';
    exit;
}

$config = $_SESSION['db_config'];
$configGenerated = false;
$configError = '';

// 生成配置文件
if (!isset($_SESSION['config_generated'])) {
    try {
        $configContent = "<?php\n";
        $configContent .= "// 启动会话\n";
        $configContent .= "if (session_status() == PHP_SESSION_NONE) {\n";
        $configContent .= "    session_start();\n";
        $configContent .= "}\n\n";
        
        $configContent .= "// 数据库配置\n";
        $configContent .= "define('DB_HOST', '" . addslashes($config['host']) . "');\n";
        $configContent .= "define('DB_USER', '" . addslashes($config['username']) . "');\n";
        $configContent .= "define('DB_PASS', '" . addslashes($config['password']) . "');\n";
        $configContent .= "define('DB_NAME', '" . addslashes($config['database']) . "');\n\n";
        
        $configContent .= "// 网站配置\n";
        $configContent .= "define('SITE_NAME', '" . addslashes($config['site_name']) . "');\n";
        $configContent .= "define('SITE_URL', '" . addslashes($config['site_url']) . "');\n\n";
        
        $configContent .= "// 安装标记\n";
        $configContent .= "define('DB_INSTALLED', true);\n\n";
        
        // 添加数据库连接函数
        $configContent .= file_get_contents(__DIR__ . '/config_functions.php');
        
        $configFile = dirname(__DIR__) . '/config.php';
        
        // 备份现有配置文件
        if (file_exists($configFile)) {
            $backupFile = $configFile . '.backup.' . date('Y-m-d-H-i-s');
            copy($configFile, $backupFile);
        }
        
        // 写入新配置文件
        if (file_put_contents($configFile, $configContent) !== false) {
            $configGenerated = true;
            $_SESSION['config_generated'] = true;
        } else {
            throw new Exception('无法写入配置文件，请检查文件权限');
        }
        
    } catch (Exception $e) {
        $configError = $e->getMessage();
    }
} else {
    $configGenerated = true;
}

// 处理完成安装
if (isset($_POST['finish']) && $configGenerated) {
    // 清理安装会话
    unset($_SESSION['db_config']);
    unset($_SESSION['install_started']);
    unset($_SESSION['install_success']);
    unset($_SESSION['config_generated']);
    
    // 重定向到网站首页
    echo '<script>window.location.href = "../index.php";</script>';
    echo '<div class="alert alert-success">安装完成！正在跳转到网站首页...</div>';
    exit;
}
?>

<h2><i class="bi bi-check-circle"></i> 安装完成</h2>
<p class="text-muted">恭喜！软件商城系统安装成功。</p>

<?php if ($configGenerated): ?>
    <div class="alert alert-success mt-4">
        <i class="bi bi-check-circle"></i>
        <strong>配置文件生成成功！</strong> 系统已准备就绪。
    </div>
    
    <!-- 安装摘要 -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-info-circle"></i> 安装摘要</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>数据库信息</h6>
                    <p><strong>服务器:</strong> <?php echo htmlspecialchars($config['host'] . ':' . $config['port']); ?></p>
                    <p><strong>数据库:</strong> <?php echo htmlspecialchars($config['database']); ?></p>
                    <p><strong>用户名:</strong> <?php echo htmlspecialchars($config['username']); ?></p>
                </div>
                <div class="col-md-6">
                    <h6>网站信息</h6>
                    <p><strong>网站名称:</strong> <?php echo htmlspecialchars($config['site_name']); ?></p>
                    <p><strong>网站地址:</strong> <?php echo htmlspecialchars($config['site_url']); ?></p>
                    <p><strong>管理员:</strong> <?php echo htmlspecialchars($config['admin_username']); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 后续步骤 -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-list-check"></i> 后续步骤</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>立即开始</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-primary"></i> 访问网站首页</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> 使用管理员账号登录</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> 进入管理后台</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> 添加商品分类和商品</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>安全建议</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-shield-check text-success"></i> 修改管理员密码</li>
                        <li><i class="bi bi-shield-check text-success"></i> 删除install目录</li>
                        <li><i class="bi bi-shield-check text-success"></i> 设置文件权限</li>
                        <li><i class="bi bi-shield-check text-success"></i> 定期备份数据库</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 管理员账号信息 -->
    <div class="card mt-4 border-warning">
        <div class="card-header bg-warning">
            <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> 重要信息</h5>
        </div>
        <div class="card-body">
            <p><strong>管理员登录信息：</strong></p>
            <div class="bg-light p-3 rounded">
                <p class="mb-1"><strong>用户名:</strong> <code><?php echo htmlspecialchars($config['admin_username']); ?></code></p>
                <p class="mb-1"><strong>密码:</strong> <code><?php echo htmlspecialchars($config['admin_password']); ?></code></p>
                <p class="mb-0"><strong>登录地址:</strong> <a href="../login.php" target="_blank"><?php echo htmlspecialchars($config['site_url']); ?>/login.php</a></p>
            </div>
            <div class="alert alert-warning mt-3 mb-0">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>安全提醒:</strong> 请立即登录并修改管理员密码，然后删除install目录以确保安全。
            </div>
        </div>
    </div>
    
    <form method="post" class="mt-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="../index.php" class="btn btn-outline-primary me-2" target="_blank">
                    <i class="bi bi-house"></i> 访问网站
                </a>
                <a href="../login.php" class="btn btn-outline-success" target="_blank">
                    <i class="bi bi-person"></i> 管理员登录
                </a>
            </div>
            <button type="submit" name="finish" class="btn btn-success btn-lg">
                <i class="bi bi-check-circle"></i> 完成安装
            </button>
        </div>
    </form>
    
<?php else: ?>
    <div class="alert alert-danger mt-4">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>配置文件生成失败！</strong> <?php echo htmlspecialchars($configError); ?>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-code"></i> 手动配置</h5>
        </div>
        <div class="card-body">
            <p>请手动创建 <code>config.php</code> 文件，内容如下：</p>
            <pre class="bg-light p-3 rounded"><code><?php
            echo htmlspecialchars("<?php\n");
            echo htmlspecialchars("// 启动会话\n");
            echo htmlspecialchars("if (session_status() == PHP_SESSION_NONE) {\n");
            echo htmlspecialchars("    session_start();\n");
            echo htmlspecialchars("}\n\n");
            echo htmlspecialchars("// 数据库配置\n");
            echo htmlspecialchars("define('DB_HOST', '" . addslashes($config['host']) . "');\n");
            echo htmlspecialchars("define('DB_USER', '" . addslashes($config['username']) . "');\n");
            echo htmlspecialchars("define('DB_PASS', '" . addslashes($config['password']) . "');\n");
            echo htmlspecialchars("define('DB_NAME', '" . addslashes($config['database']) . "');\n\n");
            echo htmlspecialchars("// 网站配置\n");
            echo htmlspecialchars("define('SITE_NAME', '" . addslashes($config['site_name']) . "');\n");
            echo htmlspecialchars("define('SITE_URL', '" . addslashes($config['site_url']) . "');\n\n");
            echo htmlspecialchars("// 安装标记\n");
            echo htmlspecialchars("define('DB_INSTALLED', true);\n");
            ?></code></pre>
        </div>
    </div>
    
    <div class="text-end mt-4">
        <a href="?step=5" class="btn btn-primary btn-lg">
            <i class="bi bi-arrow-clockwise"></i> 重新生成配置
        </a>
    </div>
<?php endif; ?>
