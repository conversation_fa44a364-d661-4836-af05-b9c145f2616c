/* 管理后台搜索框样式 */
.admin-search-form {
    position: relative;
    display: flex;
    max-width: 400px;
    width: 100%;
    margin-left: auto;
}

.admin-search-input {
    flex: 1;
    border: 2px solid #e2e8f0;
    border-radius: 8px 0 0 8px;
    padding: 10px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    outline: none;
}

.admin-search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.admin-search-input::placeholder {
    color: #94a3b8;
    font-size: 14px;
}

.admin-search-button {
    background: linear-gradient(135deg, #4f46e5, #3b82f6);
    color: white;
    border: none;
    border-radius: 0 8px 8px 0;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.admin-search-button:hover {
    background: linear-gradient(135deg, #4338ca, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-search-button i {
    font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .admin-search-form {
        max-width: 100%;
        margin-top: 15px;
    }
}