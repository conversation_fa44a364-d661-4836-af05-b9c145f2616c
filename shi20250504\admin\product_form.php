<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取商品ID（如果是编辑模式）
$productId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$isEdit = $productId > 0;

// 初始化商品数据
$product = [
    'name' => '',
    'description' => '',
    'price' => '',
    'stock' => '',
    'category_id' => '',
    'image_url' => '',
    'is_active' => 1
];

// 如果是编辑模式，获取商品数据
if ($isEdit) {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = :id");
    $stmt->execute(['id' => $productId]);
    $fetchedProduct = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$fetchedProduct) {
        setMessage('danger', '商品不存在');
        redirect('products.php');
    }
    
    $product = $fetchedProduct;
}

// 获取所有分类
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $stock = (int)($_POST['stock'] ?? 0);
    $categoryId = (int)($_POST['category_id'] ?? 0);
    $isActive = isset($_POST['is_active']) ? 1 : 0;
    
    // 验证数据
    $errors = [];
    
    if (empty($name)) {
        $errors[] = '商品名称不能为空';
    }
    
    if ($price <= 0) {
        $errors[] = '商品价格必须大于0';
    }
    
    if ($stock < 0) {
        $errors[] = '商品库存不能为负数';
    }
    
    // 处理图片上传
    $imageUrl = $product['image_url']; // 默认保持原图片
    $imagePaths = []; // 所有上传的图片路径
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $uploadDir = UPLOAD_DIR . '/products/';
    
    // 确保上传目录存在
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    // 处理多图片上传
    if (isset($_FILES['images']) && is_array($_FILES['images']['name'])) {
        for ($i = 0; $i < count($_FILES['images']['name']); $i++) {
            // 检查当前图片是否成功上传
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                // 检查文件类型
                $fileType = $_FILES['images']['type'][$i];
                if (!in_array($fileType, $allowedTypes)) {
                    $errors[] = '只允许上传JPG、PNG、GIF或WEBP格式的图片';
                    break;
                }
                
                // 生成唯一的文件名
                $fileExtension = pathinfo($_FILES['images']['name'][$i], PATHINFO_EXTENSION);
                $fileName = uniqid('product_') . '.' . $fileExtension;
                $uploadPath = $uploadDir . $fileName;
                
                // 移动上传的文件
                if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $uploadPath)) {
                    $currentPath = 'uploads/products/' . $fileName;
                    $imagePaths[] = $currentPath;
                    
                    // 第一张图片作为主图（如果没有主图）
                    if (empty($imageUrl) || $i === 0) {
                        $imageUrl = $currentPath;
                    }
                } else {
                    $errors[] = '图片上传失败';
                    break;
                }
            }
        }
    } elseif (!$isEdit && empty($imageUrl)) {
        // 新增商品时必须上传图片
        $errors[] = '请上传商品图片';
    }
    
    // 如果没有错误，保存数据
    if (empty($errors)) {
        try {
            try {
                // 开始事务
                $pdo->beginTransaction();
                
                if ($isEdit) {
                    // 更新商品
                    $stmt = $pdo->prepare("UPDATE products SET 
                        name = :name, 
                        description = :description, 
                        price = :price, 
                        stock = :stock, 
                        category_id = :category_id, 
                        image_url = :image_url, 
                        is_active = :is_active, 
                        updated_at = NOW() 
                        WHERE id = :id");
                    
                    $params = [
                        'name' => $name,
                        'description' => $description,
                        'price' => $price,
                        'stock' => $stock,
                        'category_id' => $categoryId ?: null,
                        'image_url' => $imageUrl,
                        'is_active' => $isActive,
                        'id' => $productId
                    ];
                    
                    $stmt->execute($params);
                    
                    // 如果有新上传的图片，添加到product_images表
                    if (!empty($imagePaths)) {
                        $is_first = empty($product['image_url']); // 如果原来没有主图，则第一张为主图
                        foreach ($imagePaths as $img_path) {
                            $stmt = $pdo->prepare("INSERT INTO product_images (product_id, image_url, is_primary, created_at) VALUES (:product_id, :image_url, :is_primary, NOW())");
                            $stmt->execute([
                                'product_id' => $productId,
                                'image_url' => $img_path,
                                'is_primary' => ($is_first ? 1 : 0)
                            ]);
                            $is_first = false;
                        }
                    }
                    
                    setMessage('success', '商品已更新');
                } else {
                    // 新增商品
                    $stmt = $pdo->prepare("INSERT INTO products 
                        (name, description, price, stock, category_id, image_url, is_active, created_at, updated_at) 
                        VALUES 
                        (:name, :description, :price, :stock, :category_id, :image_url, :is_active, NOW(), NOW())");
                    
                    $params = [
                        'name' => $name,
                        'description' => $description,
                        'price' => $price,
                        'stock' => $stock,
                        'category_id' => $categoryId ?: null,
                        'image_url' => $imageUrl,
                        'is_active' => $isActive
                    ];
                    
                    $stmt->execute($params);
                    
                    // 获取新插入商品的ID
                    $newProductId = $pdo->lastInsertId();
                    
                    // 插入商品图片信息到product_images表
                    if (!empty($imagePaths)) {
                        $is_first = true; // 标记第一张图片为主图
                        foreach ($imagePaths as $img_path) {
                            $stmt = $pdo->prepare("INSERT INTO product_images (product_id, image_url, is_primary, created_at) VALUES (:product_id, :image_url, :is_primary, NOW())");
                            $stmt->execute([
                                'product_id' => $newProductId,
                                'image_url' => $img_path,
                                'is_primary' => ($is_first ? 1 : 0)
                            ]);
                            $is_first = false;
                        }
                    }
                    
                    setMessage('success', '商品已添加');
                }
                
                // 提交事务
                $pdo->commit();
            } catch (PDOException $e) {
                // 回滚事务
                $pdo->rollBack();
                $errors[] = '保存失败：' . $e->getMessage();
            }
            
            redirect('products.php');
        } catch (PDOException $e) {
            $errors[] = '保存失败：' . $e->getMessage();
        }
    }
    
    // 如果有错误，更新表单数据以便重新显示
    if (!empty($errors)) {
        $product = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'stock' => $stock,
            'category_id' => $categoryId,
            'image_url' => $imageUrl,
            'is_active' => $isActive
        ];
    }
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $isEdit ? '编辑' : '添加'; ?>商品 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php">管理后台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2"></i> 仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.php">
                            <i class="bi bi-box-seam"></i> 商品管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">
                            <i class="bi bi-tags"></i> 分类管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="bi bi-receipt"></i> 订单管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="bi bi-shop"></i> 返回商城
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php">
                            <i class="bi bi-box-arrow-right"></i> 退出
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <h5><i class="bi bi-exclamation-triangle"></i> 表单验证错误：</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><?php echo $isEdit ? '编辑' : '添加'; ?>商品</h1>
            <a href="products.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回商品列表
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">商品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">商品描述</label>
                                <textarea class="form-control" id="description" name="description" rows="5"><?php echo htmlspecialchars($product['description']); ?></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="price" class="form-label">价格 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="price" name="price" value="<?php echo $product['price']; ?>" step="0.01" min="0" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="stock" class="form-label">库存 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="stock" name="stock" value="<?php echo $product['stock']; ?>" min="0" required>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="category_id" class="form-label">分类</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">-- 选择分类 --</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>" <?php echo $product['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $product['is_active'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">上架商品</label>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="images" class="form-label">商品图片 <?php echo $isEdit ? '' : '<span class="text-danger">*</span>'; ?></label>
                                <input type="file" class="form-control mb-2" id="images" name="images[]" accept="image/*" multiple>
                                <div class="form-text">支持JPG、PNG、GIF和WEBP格式，可以选择多张图片</div>
                                
                                <?php if ($product['image_url']): ?>
                                    <div class="mt-2">
                                        <div class="position-relative">
                                            <img src="../<?php echo $product['image_url']; ?>" alt="主图" class="img-thumbnail" style="max-width: 200px;">
                                            <?php
                                            // 获取主图的ID
                                            $stmt = $pdo->prepare("SELECT id FROM product_images WHERE product_id = :product_id AND is_primary = 1 LIMIT 1");
                                            $stmt->execute(['product_id' => $productId]);
                                            $primaryImage = $stmt->fetch(PDO::FETCH_ASSOC);
                                            if ($primaryImage): ?>
                                                <a href="delete_product_image.php?id=<?php echo $primaryImage['id']; ?>&product_id=<?php echo $productId; ?>" 
                                                   class="btn btn-sm btn-danger position-absolute top-0 end-0" 
                                                   onclick="return confirm('确定要删除主图吗？这将会影响商品展示。')">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                        <p class="text-muted small">当前主图</p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php 
                                // 显示商品的其他图片
                                if ($isEdit) {
                                    $stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = :product_id AND is_primary = 0");
                                    $stmt->execute(['product_id' => $productId]);
                                    $additionalImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                    
                                    if (!empty($additionalImages)) {
                                        echo '<div class="mt-3"><p class="text-muted">其他图片：</p><div class="row">';
                                        foreach ($additionalImages as $img) {
                                            echo '<div class="col-4 col-md-3 mb-2 position-relative">';
                                            echo '<img src="../'. htmlspecialchars($img['image_url']) .'" class="img-thumbnail" style="max-width: 100%;">';
                                            echo '<a href="delete_product_image.php?id='. $img['id'] .'&product_id='. $productId .'" '
                                                 . 'class="btn btn-sm btn-danger position-absolute top-0 end-0" '
                                                 . 'onclick="return confirm(\'确定要删除这张图片吗？\')">';
                                            echo '<i class="bi bi-trash"></i></a>';
                                            echo '</div>';
                                        }
                                        echo '</div></div>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> <?php echo $isEdit ? '保存修改' : '添加商品'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>