<?php
// 检查是否有数据库配置
if (!isset($_SESSION['db_config'])) {
    echo '<script>window.location.href = "step2.php";</script>';
    echo '<div class="alert alert-warning">请先配置数据库信息...</div>';
    exit;
}

$config = $_SESSION['db_config'];
$installSteps = [];
$installComplete = false;
$hasError = false;

// 执行安装
if (!isset($_SESSION['install_started'])) {
    $_SESSION['install_started'] = true;
    
    try {
        // 连接数据库
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $installSteps[] = [
            'step' => '连接数据库',
            'status' => 'success',
            'message' => '数据库连接成功'
        ];
        
        // 读取并执行SQL文件
        $sqlFile = __DIR__ . '/install.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('安装SQL文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $installSteps[] = [
            'step' => '读取安装脚本',
            'status' => 'success',
            'message' => '安装脚本读取成功'
        ];
        
        // 分割SQL语句
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $installSteps[] = [
            'step' => '解析SQL语句',
            'status' => 'success',
            'message' => '找到 ' . count($statements) . ' 条SQL语句'
        ];
        
        // 执行SQL语句
        $pdo->beginTransaction();
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $pdo->exec($statement);
            }
        }
        
        $installSteps[] = [
            'step' => '创建数据表',
            'status' => 'success',
            'message' => '数据表创建成功'
        ];
        
        // 创建管理员账号
        $adminPasswordHash = password_hash($config['admin_password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO `users` (`username`, `password`, `email`, `is_admin`, `is_active`) VALUES (?, ?, ?, 1, 1) ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email)");
        $stmt->execute([
            $config['admin_username'],
            $adminPasswordHash,
            $config['admin_email'] ?: null
        ]);
        
        $installSteps[] = [
            'step' => '创建管理员账号',
            'status' => 'success',
            'message' => '管理员账号创建成功'
        ];
        
        // 添加默认分类
        $categories = [
            ['办公软件', '办公软件和生产力工具'],
            ['开发工具', '编程开发相关软件'],
            ['设计软件', '图形设计和创意软件'],
            ['系统工具', '系统优化和管理工具'],
            ['安全软件', '杀毒软件和安全工具'],
            ['多媒体', '音视频处理软件']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO `categories` (`name`, `description`, `is_active`) VALUES (?, ?, 1)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        $installSteps[] = [
            'step' => '添加默认分类',
            'status' => 'success',
            'message' => '默认商品分类添加成功'
        ];
        
        $pdo->commit();
        
        $installSteps[] = [
            'step' => '提交事务',
            'status' => 'success',
            'message' => '数据库安装完成'
        ];
        
        $installComplete = true;
        $_SESSION['install_success'] = true;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollback();
        }
        
        $installSteps[] = [
            'step' => '安装失败',
            'status' => 'error',
            'message' => $e->getMessage()
        ];
        
        $hasError = true;
    }
} else {
    // 显示之前的安装结果
    if (isset($_SESSION['install_success'])) {
        $installComplete = true;
        $installSteps[] = [
            'step' => '安装完成',
            'status' => 'success',
            'message' => '数据库已成功安装'
        ];
    }
}

// 处理继续到下一步
if (isset($_POST['continue']) && $installComplete) {
    echo '<script>window.location.href = "?step=5";</script>';
    echo '<div class="alert alert-info">正在跳转到完成页面...</div>';
    exit;
}
?>

<h2><i class="bi bi-gear-wide-connected"></i> 安装数据库</h2>
<p class="text-muted">正在安装数据库表结构和初始数据...</p>

<div class="mt-4">
    <?php if (empty($installSteps)): ?>
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">安装中...</span>
            </div>
            <p>正在初始化安装程序...</p>
        </div>
        <script>
        setTimeout(function() {
            location.reload();
        }, 1000);
        </script>
    <?php else: ?>
        <?php foreach ($installSteps as $step): ?>
            <div class="d-flex align-items-center mb-3 p-3 border rounded">
                <div class="me-3">
                    <?php
                    switch ($step['status']) {
                        case 'success':
                            echo '<i class="bi bi-check-circle-fill text-success fs-4"></i>';
                            break;
                        case 'error':
                            echo '<i class="bi bi-x-circle-fill text-danger fs-4"></i>';
                            break;
                        case 'warning':
                            echo '<i class="bi bi-exclamation-triangle-fill text-warning fs-4"></i>';
                            break;
                        default:
                            echo '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
                    }
                    ?>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1"><?php echo htmlspecialchars($step['step']); ?></h6>
                    <p class="mb-0 text-muted"><?php echo htmlspecialchars($step['message']); ?></p>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<?php if ($installComplete): ?>
    <div class="alert alert-success mt-4">
        <i class="bi bi-check-circle"></i>
        <strong>数据库安装成功！</strong> 所有表和初始数据已创建完成。
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-person-check"></i> 管理员账号信息</h6>
        </div>
        <div class="card-body">
            <p><strong>用户名:</strong> <?php echo htmlspecialchars($config['admin_username']); ?></p>
            <p><strong>密码:</strong> <?php echo str_repeat('*', strlen($config['admin_password'])); ?></p>
            <?php if (!empty($config['admin_email'])): ?>
                <p><strong>邮箱:</strong> <?php echo htmlspecialchars($config['admin_email']); ?></p>
            <?php endif; ?>
            <div class="alert alert-warning mt-3">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>重要提醒:</strong> 请记住管理员账号信息，安装完成后请及时修改密码。
            </div>
        </div>
    </div>
    
    <form method="post" class="mt-4">
        <div class="text-end">
            <button type="submit" name="continue" class="btn btn-success btn-lg">
                完成安装 <i class="bi bi-arrow-right"></i>
            </button>
        </div>
    </form>
    
<?php elseif ($hasError): ?>
    <div class="alert alert-danger mt-4">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>安装失败！</strong> 请检查错误信息并重试。
    </div>
    
    <div class="text-end mt-4">
        <a href="?step=3" class="btn btn-secondary btn-lg me-2">
            <i class="bi bi-arrow-left"></i> 返回测试
        </a>
        <a href="?step=4" class="btn btn-primary btn-lg" onclick="sessionStorage.clear();">
            <i class="bi bi-arrow-clockwise"></i> 重新安装
        </a>
    </div>
<?php endif; ?>
