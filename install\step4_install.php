<?php
// 设置默认配置（如果没有的话）
if (!isset($_SESSION['db_config'])) {
    $_SESSION['db_config'] = [
        'host' => 'localhost',
        'port' => '3306',
        'username' => 'root',
        'password' => '111111',
        'database' => 'software_store',
        'site_name' => '软件商城',
        'site_url' => 'http://127.0.0.10',
        'admin_username' => 'admin',
        'admin_password' => 'admin123',
        'admin_email' => ''
    ];
}

$config = $_SESSION['db_config'];
$installSteps = [];
$installComplete = false;
$hasError = false;

// 检查是否已经安装过
if (isset($_SESSION['install_success'])) {
    $installComplete = true;
    $installSteps[] = [
        'step' => '检查安装状态',
        'status' => 'success',
        'message' => '数据库已安装完成'
    ];
} else {
    // 执行安装
    try {
        // 连接数据库
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $installSteps[] = [
            'step' => '连接数据库',
            'status' => 'success',
            'message' => '数据库连接成功'
        ];
        
        // 直接创建表结构
        $installSteps[] = [
            'step' => '准备安装脚本',
            'status' => 'success',
            'message' => '开始创建数据表'
        ];

        // 开始事务
        $pdo->beginTransaction();

        // 创建用户表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `is_admin` tinyint(1) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // 创建分类表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `description` text,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // 创建产品表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(200) NOT NULL,
            `description` text,
            `category_id` int(11) DEFAULT NULL,
            `version` varchar(50) DEFAULT NULL,
            `price` decimal(10,2) DEFAULT 0.00,
            `download_url` varchar(500) DEFAULT NULL,
            `image_url` varchar(500) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `is_top` tinyint(1) DEFAULT 0,
            `last_top_time` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `category_id` (`category_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // 创建购物车表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `cart_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 1,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // 创建订单表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `total_amount` decimal(10,2) NOT NULL,
            `status` enum('pending','paid','cancelled') DEFAULT 'pending',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // 创建订单项表
        $pdo->exec("
        CREATE TABLE IF NOT EXISTS `order_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` int(11) NOT NULL,
            `price` decimal(10,2) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `order_id` (`order_id`),
            KEY `product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $installSteps[] = [
            'step' => '创建数据表',
            'status' => 'success',
            'message' => '数据表创建成功'
        ];

        // 创建管理员账号
        $adminPasswordHash = password_hash($config['admin_password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO `users` (`username`, `password`, `email`, `is_admin`, `is_active`) VALUES (?, ?, ?, 1, 1) ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email)");
        $stmt->execute([
            $config['admin_username'],
            $adminPasswordHash,
            $config['admin_email'] ?: null
        ]);

        $installSteps[] = [
            'step' => '创建管理员账号',
            'status' => 'success',
            'message' => '管理员账号创建成功'
        ];

        // 添加示例分类
        $categories = [
            ['办公软件', '提高工作效率的办公类软件'],
            ['开发工具', '程序开发和编程相关工具'],
            ['系统工具', '系统优化和管理工具'],
            ['多媒体', '音视频处理和播放软件'],
            ['安全软件', '系统安全和防护软件']
        ];

        foreach ($categories as $cat) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, description) VALUES (?, ?)");
            $stmt->execute($cat);
        }

        $installSteps[] = [
            'step' => '添加示例分类',
            'status' => 'success',
            'message' => '示例分类添加成功'
        ];

        // 添加示例产品
        $products = [
            ['Microsoft Office 2021', '微软办公套件最新版本，包含Word、Excel、PowerPoint等应用', 1, '2021', 999.00, '#', 'https://via.placeholder.com/300x200?text=Office'],
            ['Visual Studio Code', '免费的轻量级代码编辑器，支持多种编程语言', 2, '1.85', 0.00, '#', 'https://via.placeholder.com/300x200?text=VSCode'],
            ['CCleaner Pro', '系统清理优化工具，清理垃圾文件和注册表', 3, '6.0', 29.99, '#', 'https://via.placeholder.com/300x200?text=CCleaner'],
            ['Adobe Photoshop 2024', '专业图像处理软件，设计师必备工具', 4, '2024', 299.00, '#', 'https://via.placeholder.com/300x200?text=Photoshop'],
            ['Kaspersky Total Security', '全面安全防护套件，保护您的电脑安全', 5, '2024', 89.99, '#', 'https://via.placeholder.com/300x200?text=Kaspersky'],
            ['WinRAR', '强大的压缩解压工具，支持多种格式', 3, '6.24', 19.99, '#', 'https://via.placeholder.com/300x200?text=WinRAR'],
            ['IntelliJ IDEA', '智能Java开发环境，提高开发效率', 2, '2023.3', 199.00, '#', 'https://via.placeholder.com/300x200?text=IntelliJ'],
            ['VLC Media Player', '免费的多媒体播放器，支持各种格式', 4, '3.0.18', 0.00, '#', 'https://via.placeholder.com/300x200?text=VLC']
        ];

        foreach ($products as $product) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO products (name, description, category_id, version, price, download_url, image_url) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($product);
        }

        $installSteps[] = [
            'step' => '添加示例产品',
            'status' => 'success',
            'message' => '示例产品添加成功'
        ];
        
        // 添加默认分类
        $categories = [
            ['办公软件', '办公软件和生产力工具'],
            ['开发工具', '编程开发相关软件'],
            ['设计软件', '图形设计和创意软件'],
            ['系统工具', '系统优化和管理工具'],
            ['安全软件', '杀毒软件和安全工具'],
            ['多媒体', '音视频处理软件']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO `categories` (`name`, `description`, `is_active`) VALUES (?, ?, 1)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        $installSteps[] = [
            'step' => '添加默认分类',
            'status' => 'success',
            'message' => '默认商品分类添加成功'
        ];
        
        $pdo->commit();
        
        $installSteps[] = [
            'step' => '提交事务',
            'status' => 'success',
            'message' => '数据库安装完成'
        ];
        
        $installComplete = true;
        $_SESSION['install_success'] = true;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollback();
        }
        
        $installSteps[] = [
            'step' => '安装失败',
            'status' => 'error',
            'message' => $e->getMessage()
        ];
        
        $hasError = true;
    }
}

// 处理继续到下一步
if (isset($_POST['continue']) && $installComplete) {
    echo '<script>window.location.href = "?step=5";</script>';
    echo '<div class="alert alert-info">正在跳转到完成页面...</div>';
    exit;
}
?>

<h2><i class="bi bi-gear-wide-connected"></i> 安装数据库</h2>
<p class="text-muted">正在安装数据库表结构和初始数据...</p>

<div class="mt-4">
    <?php if (empty($installSteps)): ?>
        <div class="text-center py-5">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                正在准备安装程序...
            </div>
            <a href="?step=4" class="btn btn-primary">开始安装</a>
        </div>
    <?php else: ?>
        <?php foreach ($installSteps as $step): ?>
            <div class="d-flex align-items-center mb-3 p-3 border rounded">
                <div class="me-3">
                    <?php
                    switch ($step['status']) {
                        case 'success':
                            echo '<i class="bi bi-check-circle-fill text-success fs-4"></i>';
                            break;
                        case 'error':
                            echo '<i class="bi bi-x-circle-fill text-danger fs-4"></i>';
                            break;
                        case 'warning':
                            echo '<i class="bi bi-exclamation-triangle-fill text-warning fs-4"></i>';
                            break;
                        default:
                            echo '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
                    }
                    ?>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1"><?php echo htmlspecialchars($step['step']); ?></h6>
                    <p class="mb-0 text-muted"><?php echo htmlspecialchars($step['message']); ?></p>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<?php if ($installComplete): ?>
    <div class="alert alert-success mt-4">
        <i class="bi bi-check-circle"></i>
        <strong>数据库安装成功！</strong> 所有表和初始数据已创建完成。
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="bi bi-person-check"></i> 管理员账号信息</h6>
        </div>
        <div class="card-body">
            <p><strong>用户名:</strong> <?php echo htmlspecialchars($config['admin_username']); ?></p>
            <p><strong>密码:</strong> <?php echo str_repeat('*', strlen($config['admin_password'])); ?></p>
            <?php if (!empty($config['admin_email'])): ?>
                <p><strong>邮箱:</strong> <?php echo htmlspecialchars($config['admin_email']); ?></p>
            <?php endif; ?>
            <div class="alert alert-warning mt-3">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>重要提醒:</strong> 请记住管理员账号信息，安装完成后请及时修改密码。
            </div>
        </div>
    </div>
    
    <form method="post" class="mt-4">
        <div class="text-end">
            <button type="submit" name="continue" class="btn btn-success btn-lg">
                完成安装 <i class="bi bi-arrow-right"></i>
            </button>
        </div>
    </form>
    
<?php elseif ($hasError): ?>
    <div class="alert alert-danger mt-4">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>安装失败！</strong> 请检查错误信息并重试。
    </div>
    
    <div class="text-end mt-4">
        <a href="?step=3" class="btn btn-secondary btn-lg me-2">
            <i class="bi bi-arrow-left"></i> 返回测试
        </a>
        <a href="?step=4" class="btn btn-primary btn-lg" onclick="sessionStorage.clear();">
            <i class="bi bi-arrow-clockwise"></i> 重新安装
        </a>
    </div>
<?php endif; ?>
