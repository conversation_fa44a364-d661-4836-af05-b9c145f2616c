/**
 * 自动隐藏提示栏的JavaScript文件
 * 用于categories.php页面，确保所有操作后不显示提示栏
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有提示栏元素
    var alerts = document.querySelectorAll('.alert');
    
    // 立即隐藏所有提示栏
    alerts.forEach(function(alert) {
        // 使用Bootstrap的Alert API关闭提示栏
        if (typeof bootstrap !== 'undefined') {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        } else {
            // 如果Bootstrap未加载，直接移除元素
            alert.style.display = 'none';
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }
    });
});