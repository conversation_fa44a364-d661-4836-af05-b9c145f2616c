/**
 * 通知框组件
 * 用于创建美观的自动消失通知框
 */
class Notification {
    /**
     * 创建一个新的通知框实例
     */
    constructor() {
        this.overlay = null;
        this.container = null;
        this.content = null;
        this.timeout = null;
    }

    /**
     * 创建通知框DOM元素
     * @param {string} message - 通知框显示的消息
     * @param {string} type - 通知类型 (success, error, info, warning)
     * @param {number} duration - 显示时长(毫秒)
     */
    createNotification(message, type = 'success', duration = 3000) {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'notification-overlay';
        
        // 创建通知框容器
        this.container = document.createElement('div');
        this.container.className = `notification-container notification-${type}`;
        
        // 创建内容区域
        this.content = document.createElement('div');
        this.content.className = 'notification-content';
        this.content.innerHTML = message;
        
        // 组装DOM结构
        this.container.appendChild(this.content);
        this.overlay.appendChild(this.container);
        
        // 添加到body
        document.body.appendChild(this.overlay);
        
        // 显示通知框
        setTimeout(() => {
            this.overlay.classList.add('show');
        }, 10);
        
        // 设置自动关闭
        this.timeout = setTimeout(() => {
            this.close();
        }, duration);
    }
    
    /**
     * 关闭通知框
     */
    close() {
        if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
        }
        
        if (this.overlay) {
            this.overlay.classList.remove('show');
            setTimeout(() => {
                if (this.overlay && this.overlay.parentNode) {
                    document.body.removeChild(this.overlay);
                }
            }, 300);
        }
    }
    
    /**
     * 显示成功通知
     * @param {string} message - 通知框显示的消息
     * @param {number} duration - 显示时长(毫秒)
     */
    success(message, duration = 3000) {
        this.createNotification(message, 'success', duration);
    }
    
    /**
     * 显示错误通知
     * @param {string} message - 通知框显示的消息
     * @param {number} duration - 显示时长(毫秒)
     */
    error(message, duration = 3000) {
        this.createNotification(message, 'error', duration);
    }
    
    /**
     * 显示信息通知
     * @param {string} message - 通知框显示的消息
     * @param {number} duration - 显示时长(毫秒)
     */
    info(message, duration = 3000) {
        this.createNotification(message, 'info', duration);
    }
    
    /**
     * 显示警告通知
     * @param {string} message - 通知框显示的消息
     * @param {number} duration - 显示时长(毫秒)
     */
    warning(message, duration = 3000) {
        this.createNotification(message, 'warning', duration);
    }
}

// 创建全局通知框实例
const notification = new Notification();

// 示例用法
// notification.success('操作成功！');
// notification.error('操作失败！');
// notification.info('提示信息');
// notification.warning('警告信息');