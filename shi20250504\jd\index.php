<?php
require_once 'config.php';

// 获取所有商品
function getAllProducts() {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          JOIN categories c ON p.category_id = c.id 
                          WHERE p.is_active = 1
                          ORDER BY p.is_top DESC, IFNULL(p.last_top_time, '1970-01-01') DESC, p.created_at DESC");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 获取所有分类
function getAllCategories() {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 根据分类ID获取商品
function getProductsByCategory($categoryId) {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          JOIN categories c ON p.category_id = c.id 
                          WHERE p.category_id = :category_id AND p.is_active = 1
                          ORDER BY p.is_top DESC, IFNULL(p.last_top_time, '1970-01-01') DESC, p.created_at DESC");
    $stmt->execute(['category_id' => $categoryId]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 获取每个分类的前十个商品
function getTopProductsByCategories() {
    $pdo = getDbConnection();
    $categories = getAllCategories();
    $result = [];
    
    foreach ($categories as $category) {
        $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                              JOIN categories c ON p.category_id = c.id 
                              WHERE p.category_id = :category_id AND p.is_active = 1
                              ORDER BY p.is_top DESC, IFNULL(p.last_top_time, '1970-01-01') DESC, p.created_at DESC LIMIT 10");
        $stmt->execute(['category_id' => $category['id']]);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($products)) {
            $result[$category['name']] = $products;
        }
    }
    return $result;
}

// 处理分类筛选
$categoryId = isset($_GET['category']) ? (int)$_GET['category'] : null;
$categories = getAllCategories();
$showAllCategories = isset($_GET['show_all']) && $_GET['show_all'] === '1';

// 获取商品数据
if ($showAllCategories) {
    $categoryProducts = getTopProductsByCategories();
} elseif ($categoryId) {
    $products = getProductsByCategory($categoryId);
} else {
    $products = getAllProducts();
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - 首页</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --jd-red: #e4393c;
            --jd-red-hover: #c81623;
        }
        .top-bar {
            background: #e3e4e5;
            color: #999;
            font-size: 12px;
            padding: 5px 0;
        }
        .search-bar {
            padding: 20px 0;
        }
        .search-input {
            border: 2px solid var(--jd-red);
            border-radius: 8px 0 0 8px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(228, 57, 60, 0.1);
        }
        .search-input:focus {
            outline: none;
            border-color: var(--jd-red-hover);
            box-shadow: 0 4px 12px rgba(228, 57, 60, 0.15);
        }
        .search-btn {
            background: var(--jd-red);
            color: white;
            border: none;
            border-radius: 0 8px 8px 0;
            padding: 12px 25px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(228, 57, 60, 0.2);
        }
        .search-btn:hover {
            background: var(--jd-red-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(228, 57, 60, 0.3);
        }
        .search-btn i {
            margin-right: 5px;
            font-size: 16px;
        }
        .category-menu {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .category-menu:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        .category-menu .nav-link {
            color: #333;
            padding: 12px 20px;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .category-menu .nav-link:hover {
            color: var(--jd-red);
            background: linear-gradient(to right, rgba(228,57,60,0.05), transparent);
            border-left: 3px solid var(--jd-red);
            transform: translateX(5px);
        }
        .category-menu .nav-link i {
            font-size: 16px;
            opacity: 0.7;
        }
        .carousel-item img {
            height: 400px;
            object-fit: cover;
        }
        .product-card {
            height: 100%;
            transition: all 0.3s;
            border: 1px solid #eee;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: var(--jd-red);
        }
        .product-img {
            height: 200px;
            object-fit: cover;
        }
        .product-price {
            color: var(--jd-red);
            font-size: 20px;
            font-weight: bold;
        }
        .btn-jd {
            background: var(--jd-red);
            color: white;
            border: none;
        }
        .btn-jd:hover {
            background: var(--jd-red-hover);
            color: white;
        }
        .footer {
            background: #f5f5f5;
            padding: 30px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="top-bar">
        <div class="container">
            <div class="row">
                <div class="col text-start">
                    <span>欢迎来到<?php echo SITE_NAME; ?></span>
                </div>
                <div class="col text-end">
                    <?php if (isLoggedIn()): ?>
                        <a href="orders.php" class="text-decoration-none text-muted me-3">我的订单</a>
                        <a href="cart.php" class="text-decoration-none text-muted me-3">
                            <i class="bi bi-cart3"></i> 购物车
                        </a>
                        <?php if (isAdmin()): ?>
                            <a href="admin/index.php" class="text-decoration-none text-muted me-3">后台管理</a>
                        <?php endif; ?>
                        <a href="logout.php" class="text-decoration-none text-muted">
                            退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)
                        </a>
                    <?php else: ?>
                        <a href="login.php" class="text-decoration-none text-muted me-3">登录</a>
                        <a href="register.php" class="text-decoration-none text-muted">注册</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-auto">
                    <h1 class="h3 mb-0"><a href="index.php" style="color: var(--jd-red); text-decoration: none;"><?php echo SITE_NAME; ?></a></h1>
                </div>
                <div class="col">
                    <div class="input-group">
                        <input type="text" class="form-control search-input" placeholder="搜索商品">
                        <button class="btn search-btn" type="button">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区 -->
    <div class="container">
        <div class="row">
            <!-- 左侧分类菜单 -->
            <div class="col-md-3">
                <div class="category-menu">
                    <div class="nav flex-column">
                        <a class="nav-link" href="index.php?show_all=1">
                            <i class="bi bi-grid"></i>
                            所有分类商品
                        </a>
                        <?php foreach ($categories as $category): ?>
                            <a class="nav-link" href="index.php?category=<?php echo $category['id']; ?>">
                                <i class="bi bi-chevron-right"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="col-md-9">
                <div class="mb-4">
                    <h3 class="border-bottom pb-2">
                        <?php if ($categoryId): ?>
                            <?php foreach ($categories as $category): ?>
                                <?php if ($category['id'] == $categoryId): ?>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            全部商品
                        <?php endif; ?>
                    </h3>
                    <div class="row row-cols-1 row-cols-md-3 g-4">
                        <?php if ($showAllCategories): ?>
                            <?php foreach ($categoryProducts as $categoryName => $products): ?>
                                <?php foreach ($products as $product): ?>
                                    <div class="col">
                                        <div class="card product-card h-100">
                                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top product-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                                <p class="card-text text-truncate"><?php echo htmlspecialchars($product['description']); ?></p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="product-price">¥<?php echo number_format($product['price'], 2); ?></span>
                                                    <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-jd">查看详情</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <?php foreach ($products as $product): ?>
                                <div class="col">
                                    <div class="card product-card h-100">
                                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top product-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                            <p class="card-text text-truncate"><?php echo htmlspecialchars($product['description']); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="product-price">¥<?php echo number_format($product['price'], 2); ?></span>
                                                <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-jd">查看详情</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <?php if (empty($products) && !$showAllCategories): ?>
                            <div class="col-12 text-center py-5">
                                <i class="bi bi-inbox fs-1 text-muted"></i>
                                <p class="mt-3 text-muted">暂无商品</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5>关于我们</h5>
                    <p class="text-muted"><?php echo SITE_NAME; ?>是您值得信赖的网上购物平台，提供优质商品和服务。</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>快速链接</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted text-decoration-none">首页</a></li>
                        <li><a href="cart.php" class="text-muted text-decoration-none">购物车</a></li>
                        <li><a href="orders.php" class="text-muted text-decoration-none">我的订单</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>联系我们</h5>
                    <ul class="list-unstyled text-muted">
                        <li><i class="bi bi-telephone"></i> ************</li>
                        <li><i class="bi bi-envelope"></i> <EMAIL></li>
                        <li><i class="bi bi-geo-alt"></i> 北京市朝阳区xxx街xxx号</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center text-muted">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>