<?php
// 编辑商品页面 - 重定向到通用商品表单页面
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

// 获取商品ID
$productId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// 如果没有提供ID，重定向到商品列表
if ($productId <= 0) {
    setMessage('warning', '未指定要编辑的商品');
    redirect('products.php');
}

// 重定向到通用商品表单页面，并传递ID参数
header("Location: product_form.php?id=$productId");
exit;