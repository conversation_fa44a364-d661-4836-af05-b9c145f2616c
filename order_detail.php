<?php
require_once 'config.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    setMessage('warning', '请先登录');
    redirect('login.php');
}

$userId = getCurrentUserId();
$pdo = getDbConnection();

// 获取订单ID
$orderId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// 验证订单是否属于当前用户
$stmt = $pdo->prepare("SELECT o.*, u.username FROM orders o 
                      JOIN users u ON o.user_id = u.id 
                      WHERE o.id = :id AND o.user_id = :user_id");
$stmt->execute([
    'id' => $orderId,
    'user_id' => $userId
]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    setMessage('danger', '订单不存在或无权访问');
    redirect('orders.php');
}

// 获取订单项
$stmt = $pdo->prepare("SELECT oi.*, p.name, p.image_url 
                      FROM order_items oi 
                      JOIN products p ON oi.product_id = p.id 
                      WHERE oi.order_id = :order_id");
$stmt->execute(['order_id' => $orderId]);
$orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取消息提示
$messages = getMessages();

// 订单状态中文名称
function getStatusName($status) {
    $statusNames = [
        'pending' => '待付款',
        'paid' => '已付款',
        'shipped' => '已发货',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statusNames[$status] ?? $status;
}

// 支付方式中文名称
function getPaymentMethodName($method) {
    $methodNames = [
        'alipay' => '支付宝',
        'wechat' => '微信支付'
    ];
    return $methodNames[$method] ?? $method;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .order-item-img {
            max-width: 80px;
            max-height: 80px;
            object-fit: cover;
        }
        .status-badge {
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">注册</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <!-- 消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>订单详情</h1>
            <a href="orders.php" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> 返回订单列表
            </a>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">订单信息</h5>
                    <?php 
                    $statusClass = [
                        'pending' => 'warning',
                        'paid' => 'info',
                        'shipped' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger'
                    ];
                    $badgeClass = $statusClass[$order['status']] ?? 'secondary';
                    ?>
                    <span class="badge bg-<?php echo $badgeClass; ?> status-badge">
                        <?php echo getStatusName($order['status']); ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>订单编号：</strong> <?php echo $order['id']; ?></p>
                        <p><strong>下单时间：</strong> <?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></p>
                        <p><strong>支付方式：</strong> <?php echo getPaymentMethodName($order['payment_method']); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>收货地址：</strong> <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                        <p><strong>订单总额：</strong> <span class="text-danger fw-bold">¥<?php echo number_format($order['total_amount'], 2); ?></span></p>
                    </div>
                </div>

                <?php if ($order['status'] == 'pending'): ?>
                    <div class="mt-3">
                        <a href="payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-success">
                            <i class="bi bi-credit-card"></i> 去支付
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">订单商品</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orderItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo !empty($item['image_url']) ? htmlspecialchars($item['image_url']) : 'uploads/default-product.jpg'; ?>" 
                                                 class="order-item-img me-3" alt="<?php echo htmlspecialchars($item['name']); ?>">
                                            <div>
                                                <?php echo htmlspecialchars($item['name']); ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>¥<?php echo number_format($item['price'], 2); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td>¥<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end fw-bold">总计：</td>
                                <td class="fw-bold text-danger">¥<?php echo number_format($order['total_amount'], 2); ?></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>