<?php
require_once 'config.php';
require_once 'alipay_config.php';

// 接收支付宝异步通知
$postData = $_POST;

// 记录通知数据到日志文件
file_put_contents('alipay_notify_log.txt', date('Y-m-d H:i:s') . ' - ' . json_encode($postData) . "\n", FILE_APPEND);

// 在实际应用中，这里应该验证通知的真实性
// 1. 验证签名
// 2. 验证通知数据中的app_id是否为本应用的app_id
// 3. 验证订单金额是否与系统中的订单金额一致

// 获取通知中的数据
$outTradeNo = isset($postData['out_trade_no']) ? $postData['out_trade_no'] : '';
$tradeStatus = isset($postData['trade_status']) ? $postData['trade_status'] : '';

// 如果订单号为空，直接返回失败
if (empty($outTradeNo)) {
    echo 'fail';
    exit;
}

// 连接数据库
$pdo = getDbConnection();

// 查询订单信息
$stmt = $pdo->prepare("SELECT * FROM orders WHERE id = :id");
$stmt->execute(['id' => $outTradeNo]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

// 如果订单不存在，返回失败
if (!$order) {
    echo 'fail';
    exit;
}

// 如果订单已经是已支付状态，直接返回成功
if ($order['status'] == 'paid') {
    echo 'success';
    exit;
}

// 根据支付宝交易状态更新订单状态
if ($tradeStatus == ALIPAY_TRADE_SUCCESS || $tradeStatus == ALIPAY_TRADE_FINISHED) {
    try {
        // 更新订单状态为已支付
        $stmt = $pdo->prepare("UPDATE orders SET status = 'paid' WHERE id = :id");
        $stmt->execute(['id' => $outTradeNo]);
        
        // 记录支付成功日志
        file_put_contents('alipay_success_log.txt', date('Y-m-d H:i:s') . ' - 订单 ' . $outTradeNo . ' 支付成功' . "\n", FILE_APPEND);
        
        echo 'success';
    } catch (PDOException $e) {
        // 记录错误日志
        file_put_contents('alipay_error_log.txt', date('Y-m-d H:i:s') . ' - ' . $e->getMessage() . "\n", FILE_APPEND);
        echo 'fail';
    }
} else {
    echo 'fail';
}