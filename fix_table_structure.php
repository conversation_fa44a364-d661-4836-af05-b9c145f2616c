<?php
require_once 'config.php';

echo "<h2>数据库表结构修复工具</h2>";

try {
    $pdo = getDbConnection();
    
    echo "<h3>检查和修复数据库表结构...</h3>";
    
    // 检查categories表结构
    $stmt = $pdo->query("DESCRIBE categories");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h4>当前categories表字段:</h4>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // 检查是否缺少is_active字段
    if (!in_array('is_active', $columns)) {
        echo "<p style='color: red;'>❌ 缺少is_active字段，正在添加...</p>";
        $pdo->exec("ALTER TABLE categories ADD COLUMN is_active tinyint(1) DEFAULT 1");
        echo "<p style='color: green;'>✅ is_active字段添加成功</p>";
        
        // 更新现有记录
        $pdo->exec("UPDATE categories SET is_active = 1 WHERE is_active IS NULL");
        echo "<p style='color: green;'>✅ 现有记录已更新</p>";
    } else {
        echo "<p style='color: green;'>✅ is_active字段已存在</p>";
    }
    
    // 检查products表结构
    $stmt = $pdo->query("DESCRIBE products");
    $productColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h4>当前products表字段:</h4>";
    echo "<ul>";
    foreach ($productColumns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // 检查products表是否缺少字段
    $requiredProductFields = ['is_active', 'is_top', 'last_top_time'];
    foreach ($requiredProductFields as $field) {
        if (!in_array($field, $productColumns)) {
            echo "<p style='color: red;'>❌ products表缺少{$field}字段，正在添加...</p>";
            switch ($field) {
                case 'is_active':
                    $pdo->exec("ALTER TABLE products ADD COLUMN is_active tinyint(1) DEFAULT 1");
                    $pdo->exec("UPDATE products SET is_active = 1 WHERE is_active IS NULL");
                    break;
                case 'is_top':
                    $pdo->exec("ALTER TABLE products ADD COLUMN is_top tinyint(1) DEFAULT 0");
                    break;
                case 'last_top_time':
                    $pdo->exec("ALTER TABLE products ADD COLUMN last_top_time timestamp NULL DEFAULT NULL");
                    break;
            }
            echo "<p style='color: green;'>✅ {$field}字段添加成功</p>";
        } else {
            echo "<p style='color: green;'>✅ {$field}字段已存在</p>";
        }
    }
    
    // 检查users表结构
    $stmt = $pdo->query("DESCRIBE users");
    $userColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h4>当前users表字段:</h4>";
    echo "<ul>";
    foreach ($userColumns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // 检查users表是否缺少字段
    $requiredUserFields = ['is_admin', 'is_active'];
    foreach ($requiredUserFields as $field) {
        if (!in_array($field, $userColumns)) {
            echo "<p style='color: red;'>❌ users表缺少{$field}字段，正在添加...</p>";
            switch ($field) {
                case 'is_admin':
                    $pdo->exec("ALTER TABLE users ADD COLUMN is_admin tinyint(1) DEFAULT 0");
                    break;
                case 'is_active':
                    $pdo->exec("ALTER TABLE users ADD COLUMN is_active tinyint(1) DEFAULT 1");
                    $pdo->exec("UPDATE users SET is_active = 1 WHERE is_active IS NULL");
                    break;
            }
            echo "<p style='color: green;'>✅ {$field}字段添加成功</p>";
        } else {
            echo "<p style='color: green;'>✅ {$field}字段已存在</p>";
        }
    }
    
    // 检查是否有管理员账号
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE is_admin = 1");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount == 0) {
        echo "<p style='color: red;'>❌ 没有管理员账号，正在创建...</p>";
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, is_admin, is_active) VALUES (?, ?, 1, 1)");
        $stmt->execute(['admin', $adminPassword]);
        echo "<p style='color: green;'>✅ 管理员账号创建成功 (用户名: admin, 密码: admin123)</p>";
    } else {
        echo "<p style='color: green;'>✅ 管理员账号已存在 ({$adminCount}个)</p>";
    }
    
    // 检查是否有分类数据
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categoryCount = $stmt->fetchColumn();
    
    if ($categoryCount == 0) {
        echo "<p style='color: red;'>❌ 没有分类数据，正在添加...</p>";
        $categories = [
            ['办公软件', '提高工作效率的办公类软件'],
            ['开发工具', '程序开发和编程相关工具'],
            ['系统工具', '系统优化和管理工具'],
            ['多媒体', '音视频处理和播放软件'],
            ['安全软件', '系统安全和防护软件']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO categories (name, description, is_active) VALUES (?, ?, 1)");
        foreach ($categories as $cat) {
            $stmt->execute($cat);
        }
        echo "<p style='color: green;'>✅ 示例分类添加成功</p>";
    } else {
        echo "<p style='color: green;'>✅ 分类数据已存在 ({$categoryCount}个)</p>";
    }
    
    // 检查是否有产品数据
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $productCount = $stmt->fetchColumn();
    
    if ($productCount == 0) {
        echo "<p style='color: red;'>❌ 没有产品数据，正在添加...</p>";
        $products = [
            ['Microsoft Office 2021', '微软办公套件最新版本', 1, '2021', 999.00, '#', 'https://via.placeholder.com/300x200?text=Office'],
            ['Visual Studio Code', '免费的代码编辑器', 2, '1.85', 0.00, '#', 'https://via.placeholder.com/300x200?text=VSCode'],
            ['CCleaner Pro', '系统清理优化工具', 3, '6.0', 29.99, '#', 'https://via.placeholder.com/300x200?text=CCleaner'],
            ['Adobe Photoshop 2024', '专业图像处理软件', 4, '2024', 299.00, '#', 'https://via.placeholder.com/300x200?text=Photoshop'],
            ['Kaspersky Total Security', '全面安全防护套件', 5, '2024', 89.99, '#', 'https://via.placeholder.com/300x200?text=Kaspersky']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO products (name, description, category_id, version, price, download_url, image_url, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)");
        foreach ($products as $product) {
            $stmt->execute($product);
        }
        echo "<p style='color: green;'>✅ 示例产品添加成功</p>";
    } else {
        echo "<p style='color: green;'>✅ 产品数据已存在 ({$productCount}个)</p>";
    }
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #d4edda; border-radius: 5px;'>";
    echo "<h3>🎉 数据库修复完成！</h3>";
    echo "<p>所有表结构和数据都已正确设置。</p>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;'>访问网站首页</a>";
    echo "<a href='admin/login.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>管理员登录</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='margin: 20px 0; padding: 15px; background: #f8d7da; border-radius: 5px;'>";
    echo "<h3>❌ 修复失败</h3>";
    echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
