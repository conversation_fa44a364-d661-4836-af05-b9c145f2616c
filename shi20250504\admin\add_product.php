<?php
require_once '../config.php';

// 获取数据库连接
$pdo = getDbConnection();

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    header('Location: /login.php');
    exit;
}

$error = '';
$success = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取表单数据
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $stock = intval($_POST['stock'] ?? 0);
    $category_id = intval($_POST['category_id'] ?? 0);
    
    // 表单验证
    if (empty($name)) {
        $error = '商品名称不能为空';
    } elseif (empty($description)) {
        $error = '商品描述不能为空';
    } elseif ($price <= 0) {
        $error = '商品价格必须大于0';
    } elseif ($stock < 0) {
        $error = '商品库存不能为负数';
    } elseif ($category_id <= 0) {
        $error = '请选择商品分类';
    } else {
        // 处理图片上传
        $image_path = ''; // 主图路径
        $image_paths = []; // 所有图片路径
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $upload_dir = '../uploads/products/';
        
        // 确保上传目录存在
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        // 处理多图片上传
        if (isset($_FILES['images']) && is_array($_FILES['images']['name'])) {
            for ($i = 0; $i < count($_FILES['images']['name']); $i++) {
                // 检查当前图片是否成功上传
                if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                    // 检查文件类型
                    $fileType = $_FILES['images']['type'][$i];
                    if (!in_array($fileType, $allowed_types)) {
                        $error = '只允许上传JPG、PNG、GIF或WEBP格式的图片';
                        break;
                    }
                    
                    // 生成唯一的文件名
                    $fileExtension = pathinfo($_FILES['images']['name'][$i], PATHINFO_EXTENSION);
                    $fileName = uniqid('product_') . '.' . $fileExtension;
                    $uploadPath = $upload_dir . $fileName;
                    
                    // 移动上传的文件
                    if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $uploadPath)) {
                        $currentPath = 'uploads/products/' . $fileName;
                        $image_paths[] = $currentPath;
                        
                        // 第一张图片作为主图
                        if (empty($image_path)) {
                            $image_path = $currentPath;
                        }
                    } else {
                        $error = '图片上传失败';
                        break;
                    }
                }
            }
        }
        
        // 如果没有错误，将商品添加到数据库
        if (empty($error)) {
            try {
                // 开始事务
                $pdo->beginTransaction();
                
                // 插入商品基本信息
                $stmt = $pdo->prepare("INSERT INTO products (name, description, price, stock, category_id, image_url, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
                $stmt->execute([$name, $description, $price, $stock, $category_id, $image_path]);
                
                // 获取新插入商品的ID
                $product_id = $pdo->lastInsertId();
                
                // 插入商品图片信息到product_images表
                if (!empty($image_paths)) {
                    $is_first = true; // 标记第一张图片为主图
                    foreach ($image_paths as $img_path) {
                        $stmt = $pdo->prepare("INSERT INTO product_images (product_id, image_url, is_primary, created_at) VALUES (?, ?, ?, NOW())");
                        $stmt->execute([$product_id, $img_path, ($is_first ? 1 : 0)]);
                        $is_first = false;
                    }
                }
                
                // 提交事务
                $pdo->commit();
                
                $success = '商品添加成功！';
                
                // 清空表单数据
                $name = $description = $image_path = '';
                $price = $stock = $category_id = 0;
            } catch (PDOException $e) {
                // 回滚事务
                $pdo->rollBack();
                $error = '数据库错误：' . $e->getMessage();
            }
        }
    }
}

// 获取所有分类
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = '无法获取分类列表：' . $e->getMessage();
    $categories = [];
}

// 页面标题
$page_title = '添加商品';

// 包含导航栏
include 'navbar.php';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="container mt-4">
        <h1><?php echo $page_title; ?></h1>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
            <div class="mb-3">
                <label for="name" class="form-label">商品名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                <div class="invalid-feedback">请输入商品名称</div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">商品描述 <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="5" required><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                <div class="invalid-feedback">请输入商品描述</div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">价格 (元) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0.01" value="<?php echo htmlspecialchars($price ?? ''); ?>" required>
                    <div class="invalid-feedback">请输入有效的价格</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="stock" class="form-label">库存数量 <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="stock" name="stock" min="0" value="<?php echo htmlspecialchars($stock ?? ''); ?>" required>
                    <div class="invalid-feedback">请输入有效的库存数量</div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="category_id" class="form-label">商品分类 <span class="text-danger">*</span></label>
                <select class="form-select" id="category_id" name="category_id" required>
                    <option value="">请选择分类</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" <?php echo (isset($category_id) && $category_id == $category['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback">请选择商品分类</div>
            </div>
            
            <div class="mb-3">
                <label for="images" class="form-label">商品图片</label>
                <input type="file" class="form-control" id="images" name="images[]" accept="image/*" multiple>
                <div class="form-text">支持JPG、PNG、GIF和WEBP格式，建议尺寸为800x800像素，可以选择多张图片</div>
            </div>
            
            <div class="mb-3">
                <button type="submit" class="btn btn-primary">添加商品</button>
                <a href="products.php" class="btn btn-secondary">返回商品列表</a>
            </div>
        </form>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 表单验证脚本
    (function() {
        'use strict';
        
        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation');
        
        // 循环并阻止提交
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                form.classList.add('was-validated');
            }, false);
        });
    })();
    </script>
</body>
</html>