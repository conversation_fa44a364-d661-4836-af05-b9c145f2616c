<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

// 获取备份文件列表
function getBackupFiles() {
    $backup_dir = __DIR__ . '/../数据库';
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
    }
    
    $files = glob($backup_dir . '/database_backup_*.sql');
    usort($files, function($a, $b) {
        return filemtime($b) - filemtime($a); // 按时间降序排列
    });
    
    $result = [];
    foreach ($files as $file) {
        $result[] = [
            'name' => basename($file),
            'path' => $file,
            'size' => formatFileSize(filesize($file)),
            'time' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    
    return $result;
}

// 格式化文件大小
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

// 处理手动备份请求
if (isset($_POST['action']) && $_POST['action'] === 'manual_backup') {
    try {
        // 包含自动备份脚本
        include_once 'auto_backup.php';
        setMessage('success', '数据库备份成功');
    } catch (Exception $e) {
        setMessage('danger', '备份失败：' . $e->getMessage());
    }
    redirect('db_backup.php');
}

// 处理删除备份文件请求
if (isset($_POST['action']) && $_POST['action'] === 'delete_backup' && isset($_POST['file_path'])) {
    $file_path = $_POST['file_path'];
    $backup_dir = __DIR__ . '/../数据库';
    
    // 安全检查：确保文件路径在备份目录内
    if (strpos($file_path, $backup_dir) === 0 && file_exists($file_path)) {
        if (unlink($file_path)) {
            setMessage('success', '备份文件已删除');
        } else {
            setMessage('danger', '删除备份文件失败');
        }
    } else {
        setMessage('danger', '无效的文件路径');
    }
    redirect('db_backup.php');
}

// 处理下载备份文件请求
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $file_name = basename($_GET['download']);
    $file_path = __DIR__ . '/../数据库/' . $file_name;
    
    if (file_exists($file_path) && strpos($file_name, 'database_backup_') === 0) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file_path));
        readfile($file_path);
        exit;
    } else {
        setMessage('danger', '文件不存在或无效的文件名');
        redirect('db_backup.php');
    }
}

$backup_files = getBackupFiles();
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库备份 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #4a90e2, #5cb3ff);
            color: white;
            font-weight: 600;
        }
        
        .btn-action {
            border-radius: 50px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .table td, .table th {
            vertical-align: middle;
        }
        
        .backup-item:hover {
            background-color: #f8f9fa;
        }
        
        .backup-actions .btn {
            padding: 5px 10px;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container mb-5">
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-download"></i> 数据库备份
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>点击下方按钮创建数据库的完整备份，包含所有表结构和数据。</p>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="manual_backup">
                            <button type="submit" class="btn btn-primary btn-action">
                                <i class="bi bi-database-down"></i> 立即备份数据库
                            </button>
                        </form>
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> 自动备份说明</h6>
                            <p class="mb-0">系统已配置自动备份功能，每24小时自动备份一次数据库。详细设置请查看 <a href="#" data-bs-toggle="modal" data-bs-target="#autoBackupModal">自动备份说明</a>。</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-upload"></i> 数据库恢复
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>您可以通过上传之前导出的SQL备份文件来恢复数据库。</p>
                        <a href="restore_db.php" class="btn btn-success btn-action">
                            <i class="bi bi-database-up"></i> 恢复数据库
                        </a>
                        <hr>
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle"></i> 注意事项</h6>
                            <p class="mb-0">恢复数据库将覆盖当前所有数据，请确保已备份重要数据。建议在网站访问量较少的时间段进行操作。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> 备份文件列表
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($backup_files)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 暂无备份文件
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>大小</th>
                                    <th>备份时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backup_files as $file): ?>
                                    <tr class="backup-item">
                                        <td><?php echo htmlspecialchars($file['name']); ?></td>
                                        <td><?php echo $file['size']; ?></td>
                                        <td><?php echo $file['time']; ?></td>
                                        <td class="backup-actions">
                                            <a href="?download=<?php echo urlencode($file['name']); ?>" class="btn btn-sm btn-primary" title="下载">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="删除" 
                                                    onclick="confirmDelete('<?php echo htmlspecialchars(addslashes($file['path'])); ?>')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 自动备份说明模态框 -->
    <div class="modal fade" id="autoBackupModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">自动备份说明</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?php 
                    $readme_content = file_get_contents(__DIR__ . '/README_AUTO_BACKUP.md');
                    // 简单的Markdown转HTML
                    $readme_content = nl2br(htmlspecialchars($readme_content));
                    $readme_content = preg_replace('/^# (.*)$/m', '<h3>$1</h3>', $readme_content);
                    $readme_content = preg_replace('/^## (.*)$/m', '<h4>$1</h4>', $readme_content);
                    $readme_content = preg_replace('/^### (.*)$/m', '<h5>$1</h5>', $readme_content);
                    echo $readme_content;
                    ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个备份文件吗？此操作无法撤销。</p>
                </div>
                <div class="modal-footer">
                    <form method="post" action="">
                        <input type="hidden" name="action" value="delete_backup">
                        <input type="hidden" name="file_path" id="deleteFilePath">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-trash"></i> 确定删除
                        </button>
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> 自动备份说明</h6>
                            <p class="mb-0">系统已配置自动备份功能，每24小时自动备份一次数据库。详细设置请查看 <a href="#" data-bs-toggle="modal" data-bs-target="#autoBackupModal">自动备份说明</a>。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 删除确认函数
        function confirmDelete(filePath) {
            // 设置要删除的文件路径
            document.getElementById('deleteFilePath').value = filePath;
            
            // 显示确认对话框
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    </script>
</body>
</html>