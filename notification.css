/**
 * 通知框样式
 */
.notification-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 20px;
    z-index: 9999;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-overlay.show {
    opacity: 1;
}

.notification-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    min-width: 300px;
    max-width: 80%;
    margin-top: 20px;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
}

.notification-overlay.show .notification-container {
    transform: translateY(0);
}

.notification-content {
    flex: 1;
    font-size: 16px;
    line-height: 1.5;
}

/* 成功通知样式 */
.notification-success {
    border-left: 4px solid #52c41a;
}

.notification-success::before {
    content: '✓';
    color: #52c41a;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
}

/* 错误通知样式 */
.notification-error {
    border-left: 4px solid #ff4d4f;
}

.notification-error::before {
    content: '✕';
    color: #ff4d4f;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
}

/* 信息通知样式 */
.notification-info {
    border-left: 4px solid #1890ff;
}

.notification-info::before {
    content: 'i';
    color: #1890ff;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
    font-style: italic;
}

/* 警告通知样式 */
.notification-warning {
    border-left: 4px solid #faad14;
}

.notification-warning::before {
    content: '!';
    color: #faad14;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
}