<?php
// 此脚本用于检查并修复数据库连接问题
require_once 'config.php';

try {
    // 尝试连接到数据库
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>数据库连接检查</h2>";
    echo "<p>成功连接到数据库: " . DB_NAME . "</p>";
    
    // 检查必要的表是否存在
    $tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items'];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
        if (count($stmt) === 0) {
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "<p>发现缺少以下表: " . implode(", ", $missingTables) . "</p>";
        echo "<p>正在创建缺失的表...</p>";
        
        // 引入初始化数据库脚本
        require_once 'init_db.php';
        
        echo "<p>表创建完成，请刷新页面查看结果。</p>";
    } else {
        echo "<p>所有必要的表都已存在。</p>";
    }
    
    echo "<p><a href='index.php'>返回首页</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>数据库连接错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    
    // 检查是否是因为数据库不存在
    if (strpos($e->getMessage(), "Unknown database") !== false) {
        echo "<p>数据库 '" . DB_NAME . "' 不存在，正在尝试创建...</p>";
        
        try {
            // 创建数据库连接（不指定数据库名）
            $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 创建数据库
            $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($sql);
            
            echo "<p>数据库创建成功，正在初始化表...</p>";
            
            // 选择新创建的数据库
            $pdo->exec("USE `" . DB_NAME . "`");
            
            // 初始化数据库表
            require_once 'init_db.php';
            
            echo "<p>数据库初始化完成！</p>";
            echo "<p><a href='index.php' class='btn btn-primary'>返回首页</a></p>";
            
        } catch (PDOException $e2) {
            echo "<div class='alert alert-danger'>";
            echo "<p>创建数据库失败: " . $e2->getMessage() . "</p>";
            echo "<p>请检查数据库用户权限或手动创建数据库。</p>";
            echo "<p><a href='install.php' class='btn btn-primary'>运行安装向导</a></p>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<p>数据库连接失败，可能是配置错误或数据库服务器未启动。</p>";
        echo "<p>错误详情: " . $e->getMessage() . "</p>";
        echo "<p>建议运行安装向导重新配置数据库连接。</p>";
        echo "<p><a href='install.php' class='btn btn-primary'>运行安装向导</a></p>";
        echo "</div>";
    }
}
?>