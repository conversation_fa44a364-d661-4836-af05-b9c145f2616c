<?php
// 支付宝支付配置
define('ALIPAY_APPID', '2021005125643508'); // 支付宝应用ID
define('ALIPAY_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApfkgDDoDDTMJYDPOq+Xv/+w/Gcg6NlQtf56pTvioJOVvUV9D3KbZu2dqCZWIYJCVA3MchQ6IW/nXFtxi2oQVVbyQsHkVXr0S6gk1uXpswNE7ovdxQQ4oThFDNXMAzYk37VqbBuLIt74KpXURLvqSorEzj+ZKvLK8Q3OJaoMPJnCuXJylPJADzcBFN09L4iGvnkBVWHwESlYk/2ZxiEhqpSj2I7YYPZs2UhOgj9rZY3uERrlIgL4He7SGQS8JULPGS/UKW8lfa00iW+L8/vr0s5QF+D/eWplElhK9TMPPaItDu5WhCN5ZTRSYVwBzgb2fDeCv8gHBGTBhSoT0dPMa4QIDAQAB'); // 支付宝公钥
define('ALIPAY_PRIVATE_KEY', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCl+SAMOgMNMwlgM86r5e//7D8ZyDo2VC1/nqlO+Kgk5W9RX0Pcptm7Z2oJlYhgkJUDcxyFDohb+dcW3GLahBVVvJCweRVevRLqCTW5emzA0Tui93FBDihOEUM1cwDNiTftWpsG4si3vgqldREu+pKisTOP5kq8srxDc4lqgw8mcK5cnKU8kAPNwEU3T0viIa+eQFVYfARKViT/ZnGISGqlKPYjthg9mzZSE6CP2tlje4RGuUiAvgd7tIZBLwlQs8ZL9QpbyV9rTSJb4vz++vSzlAX4P95amUSWEr1Mw89oi0O7laEI3llNFJhXAHOBvZ8N4K/yAcEZMGFKhPR08xrhAgMBAAECggEAXA071QwnvR5dSNny+ivhXFOu+6Gth4xQX8Ojq4UF3NDTWJI5lRuFDwdgLEifBs+plew2GSXiQwDiZm989CzlmFOo+/dr/stmvh61iVV5E8f4B36tdaNgf0vbtr096MD+H8psyPjm0eG5DeNL0H5w7YLSUjErqlSHTfG6eTSPJoXbrOdilgIznO+7DXf7qyiAHXvzXUq5WhwiKc53lmdhjAbMyqTqJ3SOYJISKrcBCHL3zhx6qkzISzgVja5M3SZ2/hHiOK9ujpWmvy1xaWN9aPtID7BLKjorRP5b71du5/kV1wJyL6J+wM2GO3OYH2EiAiTtb/NBtwxpHBHKPU79QQKBgQDUkhcagpxDuDnj7+wlC7NYSIyxWyGFkXp3f4U4PcOSTwgd4Il1Hz9F+Ma7kVS64XLzXcu002e8zdNpc/p17i8aB4WvWMAEMXs/Xe9oKfWAzKv8DtvFFRcG0ScnlZZnUNtga+ksqzz17+xOIqRUTn07SlDYC6jLZjpjnv4kMTuOLQKBgQDH4eII19ZTxLQo5ePyVLJ+nvLhiXeFlxjzPXIL+/REIIrg/E86FyDDt10ooUQV5CD2GLS4Vm4UMcAW3313nGqrRwx/XSz4oy6esmuz/6rjSovaKs3daouyUroqFgmOTFAInEWIjO017V5UrXyxHF3utsBGL/wJUZx8qqIC89ckBQKBgHNRIYl3K5CtQ3J9OK1MMKoHCZQ5bPEJ39ct7bZ6Azu+9hdHNSq9ndS2xhEBIWcUYQjXbGgXe6J4QmVlzozlftXvGL3sNpbPxR22NudaQ93wqqhd4ymCbHB9THFz/BjmS2x+fguGhdahpp8oud8miVd91Gevw408UxayLQqzTqytAoGAJZMxCriIcfboZ0fQMh358BOxVPSio1pkNlW2usoCgUwcz26e9iyi3Cf8O0cVpYfdKmY6kuyshN9VXPdmfrXI16njj43Uhmkmum6QEKZfWZsJ71lcmeMNn9gtZvv9j+KIqRW3fJQBg8G7me+DIUhT4Cw6uPcMSjQtVsI88pEkQIECgYAesfPTt9ibGuSbYYSUdchWu60XDrL2qPcpOs9jeQRrvmfsO4ZUghwWk8EUBOSEw1LW6t6v667BXCtD0xSQdzP/OrA/QyAIEn4pc0BS9mEeo73jdz/x0p2fphwP4SoA6HW3WR5E56xhvt7AHfCNi4VwAV0TgZS/J4mHRQ7g1LMeeA=='); // 应用私钥
define('ALIPAY_NOTIFY_URL', SITE_URL . '/alipay_notify.php'); // 支付宝异步通知地址
define('ALIPAY_RETURN_URL', SITE_URL . '/alipay_return.php'); // 支付宝同步返回地址
define('ALIPAY_GATEWAY', 'https://openapi.alipay.com/gateway.do'); // 支付宝网关

// 支付宝交易状态常量
define('ALIPAY_TRADE_SUCCESS', 'TRADE_SUCCESS'); // 支付成功
define('ALIPAY_TRADE_FINISHED', 'TRADE_FINISHED'); // 交易完成
define('ALIPAY_WAIT_BUYER_PAY', 'WAIT_BUYER_PAY'); // 交易创建，等待买家付款
define('ALIPAY_TRADE_CLOSED', 'TRADE_CLOSED'); // 未付款交易超时关闭，或支付完成后全额退款
