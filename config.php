<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '111111');
define('DB_NAME', 'software_store');
define('DB_PORT', '3306');

// 网站配置
define('SITE_NAME', '软件商城');
define('SITE_URL', 'http://127.0.0.10');

// 安全配置
define('SALT', 'af1e7bb2d350dd8e0ba8c6e4569e8f7c64016c35160f918dd3941a64ff3c8126');

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 数据库连接函数
function getDbConnection() {
    static $pdo = null;

    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }

    return $pdo;
}

// 检查数据库连接
function checkDbConnection() {
    try {
        $pdo = getDbConnection();
        return true;
    } catch (Exception $e) {
        return false;
    }
}
?>