<?php
// 设置脚本可以长时间运行
set_time_limit(300);

// 引入配置文件
require_once __DIR__ . '/../config.php';

// 定义日志函数
function logMessage($message) {
    $logFile = __DIR__ . '/../数据库/backup_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    echo $logMessage; // 同时输出到控制台
}

// 检查是否为命令行执行
$isCLI = (php_sapi_name() === 'cli');

// 如果是通过浏览器访问，检查是否为管理员
if (!$isCLI && (!isLoggedIn() || !isAdmin())) {
    die('此脚本只能由管理员或通过计划任务执行');
}

try {
    logMessage('开始执行数据库自动备份');
    
    $pdo = getDbConnection();
    
    // 设置导出文件名
    $timestamp = date('Y-m-d_H-i-s');
    $filename = "database_backup_{$timestamp}.sql";
    $backup_dir = __DIR__ . '/../数据库';

    // 确保备份目录存在
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
        logMessage("创建备份目录: $backup_dir");
    }

    $backup_file = $backup_dir . '/' . $filename;
    $output = '';
    
    // 获取数据库名称
    $dbName = DB_NAME;
    
    // 添加创建数据库和使用数据库语句
    $output .= "-- 创建数据库\n";
    $output .= "CREATE DATABASE IF NOT EXISTS `{$dbName}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;\n";
    $output .= "USE `{$dbName}`;\n\n";

    // 获取所有视图
    $views = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE LIKE 'VIEW'")->fetchAll(PDO::FETCH_COLUMN);
    
    // 删除所有视图
    $output .= "\n-- 删除所有视图\n";
    foreach ($views as $view) {
        $output .= "DROP VIEW IF EXISTS `{$view}`;\n";
    }
    
    // 删除所有存储过程
    $output .= "\n-- 删除所有存储过程\n";
    $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($procedures as $procedure) {
        $output .= "DROP PROCEDURE IF EXISTS `{$procedure}`;\n";
    }
    
    // 删除所有函数
    $output .= "\n-- 删除所有函数\n";
    $functions = $pdo->query("SHOW FUNCTION STATUS WHERE Db = '{$dbName}'")->fetchAll(PDO::FETCH_COLUMN, 1);
    foreach ($functions as $function) {
        $output .= "DROP FUNCTION IF EXISTS `{$function}`;\n";
    }
    
    // 删除所有事件
    $output .= "\n-- 删除所有事件\n";
    $events = $pdo->query("SHOW EVENTS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($events as $event) {
        $output .= "DROP EVENT IF EXISTS `{$event}`;\n";
    }
    
    // 删除所有触发器
    $output .= "\n-- 删除所有触发器\n";
    $triggers = $pdo->query("SHOW TRIGGERS")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($triggers as $trigger) {
        $output .= "DROP TRIGGER IF EXISTS `{$trigger}`;\n";
    }
    
    // 定义表的创建顺序，确保外键约束不会出错
    $tables = array(
        'users',      // 基础表，无外键依赖
        'categories', // 基础表，无外键依赖
        'products',   // 依赖于categories表（外键约束）
        'orders',     // 依赖于users表
        'order_items', // 依赖于orders和products表
        'cart_items'  // 依赖于users和products表
    );
    
    // 验证所有表是否存在
    $existingTables = $pdo->query("SHOW FULL TABLES WHERE TABLE_TYPE = 'BASE TABLE'")->fetchAll(PDO::FETCH_COLUMN);
    $tables = array_intersect($tables, $existingTables);
    
    foreach ($tables as $table) {
        // 添加删除表和创建表语句
        $output .= "\n-- 导出 {$table} 表结构\n";
        $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
        
        $create_table = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch(PDO::FETCH_ASSOC);
        $output .= $create_table['Create Table'] . ";\n\n";

        // 获取表数据
        $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $output .= "-- 导出 {$table} 表数据\n";
            $columns = array_keys($rows[0]);
            $output .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = $pdo->quote($value);
                    }
                }
                $values[] = "(" . implode(', ', $rowValues) . ")";
            }
            $output .= implode(",\n", $values) . ";\n\n";
        }
    }
    
    // 写入SQL文件
    if (file_put_contents($backup_file, $output)) {
        logMessage("数据库备份成功: $filename");
        
        // 清理旧备份文件（保留最近10个备份）
        $backup_files = glob($backup_dir . '/database_backup_*.sql');
        usort($backup_files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        if (count($backup_files) > 10) {
            $files_to_delete = array_slice($backup_files, 10);
            foreach ($files_to_delete as $file) {
                if (unlink($file)) {
                    logMessage("删除旧备份文件: " . basename($file));
                }
            }
        }
        
        logMessage('自动备份完成');
    } else {
        throw new Exception("无法写入备份文件: $backup_file");
    }
    
} catch (Exception $e) {
    $error_message = '自动备份失败: ' . $e->getMessage();
    logMessage($error_message);
    
    // 如果是通过浏览器访问，显示错误信息
    if (!$isCLI) {
        echo "<div style='color:red;'>$error_message</div>";
    }
}

// 如果是通过浏览器访问，提供返回链接
if (!$isCLI) {
    echo "<p><a href='users.php'>返回用户管理</a></p>";
}