/**
 * 自动关闭的消息提示框组件
 */
class AutoMessage extends Dialog {
    /**
     * 创建一个新的自动关闭消息提示框实例
     * @param {string} message - 提示消息
     * @param {string} type - 消息类型 (success, danger, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    constructor(message, type = 'info', duration = 3000) {
        super();
        this.message = message;
        this.type = type;
        this.duration = duration;
        this.timer = null;
    }

    /**
     * 创建消息提示框DOM元素
     */
    createMessage() {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'dialog-overlay';
        this.overlay.style.background = 'transparent';
        
        // 创建消息容器
        this.container = document.createElement('div');
        this.container.className = 'dialog-container auto-message';
        this.container.style.padding = '20px 32px';
        this.container.style.width = 'auto';
        this.container.style.minWidth = '300px';
        this.container.style.maxWidth = '600px';
        
        // 设置不同类型的样式
        const colors = {
            success: '#10b981',
            danger: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        
        const color = colors[this.type] || colors.info;
        this.container.style.borderLeft = `4px solid ${color}`;
        
        // 创建内容区域
        this.content = document.createElement('div');
        this.content.className = 'dialog-content';
        this.content.style.margin = '0';
        this.content.style.textAlign = 'left';
        this.content.textContent = this.message;
        
        // 组装DOM结构
        this.container.appendChild(this.content);
        this.overlay.appendChild(this.container);
        
        // 添加到body
        document.body.appendChild(this.overlay);
        
        // 显示消息
        setTimeout(() => {
            this.overlay.classList.add('show');
        }, 10);
        
        // 设置自动关闭定时器
        this.timer = setTimeout(() => {
            this.close();
        }, this.duration);
    }

    /**
     * 显示消息
     */
    show() {
        this.createMessage();
    }

    /**
     * 关闭消息
     */
    close() {
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }
        
        if (this.overlay) {
            this.overlay.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(this.overlay);
                this.overlay = null;
            }, 400);
        }
    }
}

// 在页面加载完成后，检查并显示消息
document.addEventListener('DOMContentLoaded', () => {
    const messages = document.querySelectorAll('.alert');
    if (messages.length > 0) {
        messages.forEach(alert => {
            const type = alert.classList.toString().match(/alert-([^\s]+)/)[1];
            const message = alert.textContent.trim();
            new AutoMessage(message, type).show();
            alert.remove();
        });
    }
});