<?php
require_once 'alipay_config.php';

/**
 * 支付宝SDK集成类
 */
class AlipaySDK {
    private $appId;
    private $privateKey;
    private $publicKey;
    private $gateway;
    
    public function __construct() {
        $this->appId = ALIPAY_APPID;
        $this->privateKey = ALIPAY_PRIVATE_KEY;
        $this->publicKey = ALIPAY_PUBLIC_KEY;
        $this->gateway = ALIPAY_GATEWAY;
    }
    
    /**
     * 生成支付二维码
     * @param array $params 支付参数
     * @return string 支付二维码URL
     */
    public function generateQRCode($params) {
        // 构建请求参数
        $bizContent = [
            'out_trade_no' => $params['out_trade_no'],
            'total_amount' => $params['total_amount'],
            'subject' => $params['subject'],
            'product_code' => 'FACE_TO_FACE_PAYMENT'
        ];
        
        $requestParams = [
            'app_id' => $this->appId,
            'method' => 'alipay.trade.precreate',
            'format' => 'JSON',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => ALIPAY_NOTIFY_URL,
            'biz_content' => json_encode($bizContent)
        ];
        
        // 生成签名
        $requestParams['sign'] = $this->generateSign($requestParams);
        
        // 发送请求
        $response = $this->sendRequest($requestParams);
        $result = json_decode($response, true);
        
        // 返回二维码URL
        if (isset($result['alipay_trade_precreate_response']['qr_code'])) {
            return $result['alipay_trade_precreate_response']['qr_code'];
        }
        
        return null;
    }
    
    /**
     * 生成签名
     * @param array $params 请求参数
     * @return string 签名
     */
    private function generateSign($params) {
        // 1. 对参数进行排序
        ksort($params);
        
        // 2. 构建签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        // 3. 生成签名
        $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" . 
            chunk_split($this->privateKey, 64, "\n") . 
            "-----END RSA PRIVATE KEY-----";
        
        $res = openssl_get_privatekey($privateKey);
        if (!$res) {
            error_log('无法加载私钥: ' . openssl_error_string());
            return '';
        }
        
        $sign = '';
        $result = openssl_sign($signStr, $sign, $res, OPENSSL_ALGO_SHA256);
        // 移除已弃用的openssl_free_key调用，PHP会自动处理资源释放
        
        if (!$result || empty($sign)) {
            error_log('签名生成失败: ' . openssl_error_string());
            return '';
        }
        
        return base64_encode($sign);
    }
    
    /**
     * 发送HTTP请求
     * @param array $params 请求参数
     * @return string 响应结果
     */
    private function sendRequest($params) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->gateway);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }
}