<?php
require_once 'config.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// 获取当前用户ID
$userId = $_SESSION['user_id'];

// 获取数据库连接
$pdo = getDbConnection();

// 处理订单状态更新
if (isset($_POST['action']) && isset($_POST['order_id'])) {
    $orderId = (int)$_POST['order_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'update_status' && isset($_POST['status'])) {
            $newStatus = $_POST['status'];
            
            // 验证订单属于当前用户
            $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = :id AND user_id = :user_id");
            $stmt->execute([
                'id' => $orderId,
                'user_id' => $userId
            ]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($order && $order['status'] === 'pending' && $newStatus === 'cancelled') {
                $stmt = $pdo->prepare("UPDATE orders SET status = :status, updated_at = NOW() WHERE id = :id AND user_id = :user_id");
                $stmt->execute([
                    'status' => $newStatus,
                    'id' => $orderId,
                    'user_id' => $userId
                ]);
                setMessage('success', '订单已成功取消');
                
                // 取消订单后重定向到已取消订单列表
                header('Location: orders.php?status=cancelled');
                exit;
            } else {
                setMessage('danger', '操作失败：无效的订单或操作');
            }
        }
    } catch (PDOException $e) {
        setMessage('danger', '操作失败：' . $e->getMessage());
    }
    
    // 根据当前状态重定向到相应的订单列表
    $redirectStatus = isset($_POST['status']) ? $_POST['status'] : '';
    header('Location: orders.php' . ($redirectStatus ? "?status=$redirectStatus" : ''));
    exit;
}

// 获取状态筛选参数
$status = isset($_GET['status']) ? $_GET['status'] : '';

// 构建SQL查询
$sql = "SELECT * FROM orders WHERE user_id = :user_id";
if ($status !== '') {
    $sql .= " AND status = :status";
}
$sql .= " ORDER BY created_at DESC";

// 准备并执行查询
$stmt = $pdo->prepare($sql);
$params = ['user_id' => $userId];
if ($status !== '') {
    $params['status'] = $status;
}
$stmt->execute($params);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取消息提示
$messages = getMessages();

// 订单状态名称映射
function getStatusName($status) {
    $statusNames = [
        'pending' => '待支付',
        'paid' => '已支付',
        'shipped' => '已发货',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statusNames[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">首页</a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="cart.php">
                                <i class="bi bi-cart"></i> 购物车
                                <span class="badge bg-danger rounded-pill"><?php echo getCartItemCount(); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="orders.php">我的订单</a>
                        </li>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="admin/index.php">后台管理</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">退出 (<?php echo htmlspecialchars($_SESSION['username']); ?>)</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>我的订单</h1>
            <div class="btn-group">
                <a href="orders.php" class="btn btn-outline-primary <?php echo $status === '' ? 'active' : ''; ?>">
                    全部
                </a>
                <a href="orders.php?status=pending" class="btn btn-outline-warning <?php echo $status === 'pending' ? 'active' : ''; ?>">
                    待支付
                </a>
                <a href="orders.php?status=paid" class="btn btn-outline-info <?php echo $status === 'paid' ? 'active' : ''; ?>">
                    已支付
                </a>
                <a href="orders.php?status=shipped" class="btn btn-outline-primary <?php echo $status === 'shipped' ? 'active' : ''; ?>">
                    已发货
                </a>
                <a href="orders.php?status=completed" class="btn btn-outline-success <?php echo $status === 'completed' ? 'active' : ''; ?>">
                    已完成
                </a>
                <a href="orders.php?status=cancelled" class="btn btn-outline-danger <?php echo $status === 'cancelled' ? 'active' : ''; ?>">
                    已取消
                </a>
            </div>
        </div>

        <?php if (empty($orders)): ?>
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> 暂无订单记录
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>订单编号</th>
                            <th>下单时间</th>
                            <th>订单金额</th>
                            <th>订单状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?php echo $order['id']; ?></td>
                                <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
                                <td class="text-danger fw-bold">¥<?php echo number_format($order['total_amount'], 2); ?></td>
                                <td>
                                    <?php 
                                    $statusClass = [
                                        'pending' => 'warning',
                                        'paid' => 'info',
                                        'shipped' => 'primary',
                                        'completed' => 'success',
                                        'cancelled' => 'danger'
                                    ];
                                    $badgeClass = $statusClass[$order['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $badgeClass; ?>">
                                        <?php echo getStatusName($order['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="order_detail.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </a>
                                    <?php if ($order['status'] === 'pending'): ?>
                                        <a href="payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-sm btn-success">
                                            <i class="bi bi-credit-card"></i> 去支付
                                        </a>
                                        <form method="post" class="d-inline" onsubmit="return confirm('确定要取消该订单吗？')">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="status" value="cancelled">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="bi bi-x-circle"></i> 取消订单
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>