<?php
// 在任何输出之前启用输出缓冲
ob_start();

// 如果是AJAX请求，立即设置Content-Type
if (isset($_POST['action']) && $_POST['action'] === 'restore') {
    header('Content-Type: application/json; charset=utf-8');
}

require_once '../config.php';

// 处理AJAX请求 - 将AJAX处理移到文件最前面，确保没有其他输出
if (isset($_POST['action']) && $_POST['action'] === 'restore') {
    // 禁用错误显示
    ini_set('display_errors', 0);
    error_reporting(0);
    
    // 清除之前的所有输出缓冲
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // 重新启动输出缓冲以捕获任何可能的错误输出
    ob_start();
    
    try {
        // 检查用户是否已登录且是管理员
        if (!isLoggedIn() || !isAdmin()) {
            throw new Exception('请先登录管理员账号');
        }
        
        if (!isset($_FILES['backup_file'])) {
            throw new Exception('没有上传文件');
        }
        
        $file = $_FILES['backup_file'];
        $allowedExtensions = ['sql'];
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $maxFileSize = 100 * 1024 * 1024; // 100MB

        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('文件上传失败：' . getUploadErrorMessage($file['error']));
        } elseif (!in_array($fileExtension, $allowedExtensions)) {
            throw new Exception('只允许上传SQL文件');
        } elseif ($file['size'] > $maxFileSize) {
            throw new Exception('文件大小不能超过100MB');
        }

        // 读取SQL文件内容
        $sqlContent = @file_get_contents($file['tmp_name']);
        if ($sqlContent === false) {
            throw new Exception('无法读取上传的文件');
        }
        
        // 获取数据库连接
        $pdo = getDbConnection();
        
        try {
            // 开始事务
            $pdo->beginTransaction();
            
            // 禁用外键检查
            $pdo->exec('SET FOREIGN_KEY_CHECKS = 0');
            
            // 获取所有现有表
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // 删除所有现有表
            foreach ($tables as $table) {
                $pdo->exec("DROP TABLE IF EXISTS `$table`");
            }
            
            // 将SQL内容分割成单独的语句
            $sqlStatements = preg_split("/;\s*\n/", $sqlContent);
            $totalStatements = count($sqlStatements);
            $completedStatements = 0;
            
            foreach ($sqlStatements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        $completedStatements++;
                    } catch (PDOException $e) {
                        throw new Exception('执行SQL语句失败：' . $e->getMessage());
                    }
                }
            }
            
            // 启用外键检查
            $pdo->exec('SET FOREIGN_KEY_CHECKS = 1');
            
            // 提交事务（如果有活动的事务）
            if ($pdo->inTransaction()) {
                $pdo->commit();
            }
        } catch (PDOException $e) {
            // 如果发生PDO异常，回滚事务并重新抛出异常
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            throw new Exception('数据库操作失败：' . $e->getMessage());
        }
        
        // 清除任何可能的输出
        ob_end_clean();
        
        // 输出JSON响应
        echo json_encode(['success' => true, 'progress' => 100, 'message' => '数据库恢复完成'], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        // 如果有事务正在进行，尝试回滚
        if (isset($pdo) && $pdo instanceof PDO) {
            try {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                
                // 确保外键检查被重新启用
                $pdo->exec('SET FOREIGN_KEY_CHECKS = 1');
            } catch (Exception $ex) {
                // 记录回滚过程中的错误，但不影响主错误响应
                error_log('回滚事务时发生错误: ' . $ex->getMessage());
            }
        }
        
        // 清除任何可能的输出
        ob_end_clean();
        
        // 输出JSON错误响应
        echo json_encode(['success' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
    
    // 确保脚本在此处终止
    exit;
}

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

// 获取文件上传错误信息
function getUploadErrorMessage($error) {
    switch ($error) {
        case UPLOAD_ERR_INI_SIZE:
            return '上传的文件超过了php.ini中upload_max_filesize的限制';
        case UPLOAD_ERR_FORM_SIZE:
            return '上传的文件超过了HTML表单中MAX_FILE_SIZE的限制';
        case UPLOAD_ERR_PARTIAL:
            return '文件只有部分被上传';
        case UPLOAD_ERR_NO_FILE:
            return '没有文件被上传';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '找不到临时文件夹';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return '文件上传被PHP扩展程序中断';
        default:
            return '未知的上传错误';
    }
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恢复数据库 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        /* 自定义对话框样式 */
        .modal-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.95);
            width: auto;
            max-width: 500px;
            margin: 0;
            opacity: 0;
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
        }

        .modal.show .modal-dialog {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        
        .modal-content {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            overflow: hidden;
        }
        
        .modal-header {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #4a90e2, #5cb3ff);
            color: white;
            padding: 20px;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .modal-body {
            padding: 25px;
            font-size: 16px;
            line-height: 1.6;
            color: #2c3e50;
        }
        
        .modal-footer {
            border-top: none;
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .modal-btn {
            border-radius: 30px;
            padding: 10px 30px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 120px;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), transparent);
            transition: transform 0.6s;
        }

        .modal-btn:hover::before {
            transform: translateX(200%);
        }
        
        .modal-btn-confirm {
            background: linear-gradient(135deg, #4a90e2, #5cb3ff);
            color: white;
        }
        
        .modal-btn-confirm:hover {
            background: linear-gradient(135deg, #357abd, #4a90e2);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(74,144,226,0.3);
        }
        
        .modal-btn-cancel {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
        }
        
        .modal-btn-cancel:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        }
        
        .modal-icon {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
            animation: icon-bounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .modal-backdrop {
            background-color: rgba(0,0,0,0.6);
            backdrop-filter: blur(5px);
            transition: opacity 0.3s ease-out;
        }

        @keyframes icon-bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        /* 原有的alert样式优化 */
        .alert {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
            min-width: 350px;
            max-width: 80%;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease-in-out;
            border-radius: 15px;
            border: none;
            padding: 20px;
            animation: alert-appear 0.3s ease-out;
        }
        
        @keyframes alert-appear {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="../index.php">京东商城</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2"></i> 仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="bi bi-box-seam"></i> 商品管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">
                            <i class="bi bi-tags"></i> 分类管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="bi bi-receipt"></i> 订单管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="bi bi-shop"></i> 返回商城
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php">
                            <i class="bi bi-box-arrow-right"></i> 退出
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mb-4">
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-upload"></i> 恢复数据库
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="restoreForm" enctype="multipart/form-data" onsubmit="return handleRestore(event)">
                            <input type="hidden" name="action" value="restore">
                            <div class="mb-3">
                                <label for="backup_file" class="form-label">选择备份文件</label>
                                <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                                <div class="form-text text-muted">
                                    请选择之前导出的SQL备份文件（.sql格式，最大100MB）
                                </div>
                            </div>
                            
                            <!-- 进度条 -->
                            <div id="progressContainer" class="d-none">
                                <div class="progress mb-3" style="height: 25px; background-color: #e9ecef; border-radius: 15px; box-shadow: inset 0 1px 2px rgba(0,0,0,.1);">
                                    <div id="progressBar" 
                                         class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: 0%; background-image: linear-gradient(45deg, #007bff, #00bcd4); border-radius: 15px; transition: width .6s ease;" 
                                         aria-valuenow="0" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        <span class="progress-text">0%</span>
                                    </div>
                                </div>
                                <p id="progressMessage" class="text-center text-muted fw-bold"></p>
                            </div>

                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> 注意事项：
                                <ul class="mb-0">
                                    <li>恢复数据库将覆盖当前所有数据，请确保已备份重要数据</li>
                                    <li>恢复过程中请勿刷新页面或关闭浏览器</li>
                                    <li>建议在网站访问量较少的时间段进行操作</li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-upload"></i> 开始恢复
                                </button>
                                <a href="db_backup.php" class="btn btn-secondary ms-2">
                                    <i class="bi bi-x"></i> 取消
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // 添加自定义确认对话框
    function showConfirmDialog(message, onConfirm, onCancel) {
        // 创建模态对话框
        const modalHtml = `
            <div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="bi bi-exclamation-triangle modal-icon"></i>确认操作</h5>
                        </div>
                        <div class="modal-body">
                            ${message}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="modal-btn modal-btn-cancel" id="cancelBtn">取消</button>
                            <button type="button" class="modal-btn modal-btn-confirm" id="confirmBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到文档中
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer);
        
        // 获取模态对话框实例
        const modalElement = document.getElementById('confirmModal');
        const modal = new bootstrap.Modal(modalElement);
        
        // 绑定按钮事件
        document.getElementById('confirmBtn').addEventListener('click', function() {
            modal.hide();
            if (typeof onConfirm === 'function') onConfirm();
            // 移除模态对话框
            modalElement.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modalContainer);
            });
        });
        
        document.getElementById('cancelBtn').addEventListener('click', function() {
            modal.hide();
            if (typeof onCancel === 'function') onCancel();
            // 移除模态对话框
            modalElement.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modalContainer);
            });
        });
        
        // 显示模态对话框
        modal.show();
    }
    
    function handleRestore(event) {
        event.preventDefault();
        
        // 使用自定义对话框替代原生confirm
        showConfirmDialog('<p class="mb-3 fw-bold">警告：恢复数据库将覆盖当前所有数据！</p><p>请确保已备份重要数据。</p><p>是否继续？</p>', function() {
            // 用户点击确认后执行
            const form = event.target;
            const formData = new FormData(form);
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressMessage = document.getElementById('progressMessage');
            
            // 禁用提交按钮并显示进度条
            submitBtn.disabled = true;
            progressContainer.classList.remove('d-none');
            progressMessage.textContent = '正在处理，请稍候...';
        
            // 明确指定请求URL为当前页面，避免404错误
            fetch('restore_db.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // 检查响应状态
                if (!response.ok) {
                    throw new Error('服务器响应错误：' + response.status);
                }
                
                // 尝试解析JSON，即使Content-Type不是application/json
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('服务器返回了非JSON格式的响应：' + text.substring(0, 100) + '...');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    // 更新进度条
                    progressBar.style.width = data.progress + '%';
                    progressBar.textContent = data.progress + '%';
                    progressBar.setAttribute('aria-valuenow', data.progress);
                    progressMessage.textContent = data.message;
                    
                    if (data.progress === 100) {
                        // 恢复完成，直接跳转到数据库备份页面，不显示成功消息
                        progressBar.classList.remove('progress-bar-animated');
                        setTimeout(() => {
                            window.location.href = 'db_backup.php';
                        }, 500);
                    }
                } else {
                    throw new Error(data.message || '未知错误');
                }
            })
            .catch(error => {
                // 使用自定义对话框替代原生alert
                showConfirmDialog('<p class="text-danger fw-bold"><i class="bi bi-x-circle-fill me-2"></i>错误</p><p>' + error.message + '</p>', function() {
                    submitBtn.disabled = false;
                    progressContainer.classList.add('d-none');
                });
            });
        });
        
        return false;
    }
    </script>
</body>
</html>