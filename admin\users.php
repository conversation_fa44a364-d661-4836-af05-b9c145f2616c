<?php
require_once '../config.php';

// 检查用户是否已登录且是管理员
if (!isLoggedIn() || !isAdmin()) {
    setMessage('warning', '请先登录管理员账号');
    redirect('../login.php');
}

$pdo = getDbConnection();

// 获取搜索参数
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$perPage = 10;

// 构建查询
$sql = "SELECT SQL_CALC_FOUND_ROWS * FROM users WHERE 1";
$params = [];

if ($search) {
    $sql .= " AND (username LIKE :search OR email LIKE :search OR nickname LIKE :search)";
    $params['search'] = "%$search%";
}

$sql .= " ORDER BY created_at DESC LIMIT :offset, :limit";
$offset = ($page - 1) * $perPage;

// 执行查询
$stmt = $pdo->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取总记录数
$stmt = $pdo->query("SELECT FOUND_ROWS()");
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 处理用户状态更新
if (isset($_POST['action']) && isset($_POST['user_id'])) {
    $userId = (int)$_POST['user_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'toggle_status') {
            $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE id = :id AND is_admin = 0");
            $stmt->execute(['id' => $userId]);
            setMessage('success', '用户状态已更新');
        } elseif ($action === 'set_admin') {
            $stmt = $pdo->prepare("UPDATE users SET is_admin = 1 WHERE id = :id AND is_admin = 0");
            $stmt->execute(['id' => $userId]);
            setMessage('success', '已将该用户设置为管理员');
        } elseif ($action === 'set_user') {
            // 检查系统中是否还有其他管理员，确保至少有一个管理员账号
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE is_admin = 1 AND id != :id");
            $stmt->execute(['id' => $userId]);
            $adminCount = $stmt->fetchColumn();
            
            if ($adminCount > 0) {
                $stmt = $pdo->prepare("UPDATE users SET is_admin = 0 WHERE id = :id AND is_admin = 1");
                $stmt->execute(['id' => $userId]);
                setMessage('success', '已将该用户设置为一般用户');
            } else {
                setMessage('danger', '操作失败：系统中必须至少保留一个管理员账号');
            }
        }
    } catch (PDOException $e) {
        setMessage('danger', '操作失败：' . $e->getMessage());
    }
    
    redirect('users.php' . ($search ? "?search=$search" : ''));
}

// 获取消息提示
$messages = getMessages();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin-search.css">
    <link rel="stylesheet" href="../dialog.css">
    <link rel="stylesheet" href="css/custom-dialog.css">
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container">
        <!-- 显示消息提示 -->
        <?php foreach ($messages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endforeach; ?>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>用户管理</h1>
            <form class="d-flex" method="get">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="搜索用户名/邮箱/昵称..." value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                </div>
            </form>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">用户列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>昵称</th>
                                <th>邮箱</th>
                                <th>注册时间</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">暂无用户数据</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['nickname'] ?? '未设置'); ?></td>
                                        <td><?php echo htmlspecialchars($user['email'] ?? ''); ?></td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['is_admin'] ? 'primary' : 'secondary'; ?>">
                                                <?php echo $user['is_admin'] ? '管理员' : '普通用户'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['is_active'] ? 'success' : 'danger'; ?>">
                                                <?php echo $user['is_active'] ? '正常' : '禁用'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if (!$user['is_admin']): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <button type="submit" class="btn btn-outline-<?php echo $user['is_active'] ? 'warning' : 'success'; ?>" title="<?php echo $user['is_active'] ? '禁用账号' : '启用账号'; ?>">
                                                            <i class="bi bi-<?php echo $user['is_active'] ? 'slash-circle' : 'check-circle'; ?>"></i>
                                                        </button>
                                                    </form>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="set_admin">
                                                        <button type="submit" class="btn btn-outline-primary" title="设为管理员">
                                                            <i class="bi bi-shield"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <input type="hidden" name="action" value="set_user">
                                                        <button type="submit" class="btn btn-outline-secondary" title="取消管理员">
                                                            <i class="bi bi-person"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $startPage + 4);
                            if ($endPage - $startPage < 4) {
                                $startPage = max(1, $endPage - 4);
                            }
                            ?>
                            
                            <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        下一页
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>