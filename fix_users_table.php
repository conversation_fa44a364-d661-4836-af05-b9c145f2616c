<?php
require_once 'config.php';

// 检查数据库中是否存在is_active字段，如果不存在则添加
try {
    $pdo = getDbConnection();
    
    // 检查users表中是否已有is_active字段
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'is_active'");
    $stmt->execute();
    $isActiveExists = $stmt->rowCount() > 0;
    
    if (!$isActiveExists) {
        // 添加is_active字段
        $sql = "ALTER TABLE users ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER is_admin";
        $pdo->exec($sql);
        
        echo "成功添加用户状态字段并设置默认值为启用。<br>";
    } else {
        echo "用户状态字段已存在，无需修改。<br>";
    }
    
    echo "<a href='admin/users.php'>返回用户管理</a>";
    
} catch (PDOException $e) {
    die("数据库操作失败: " . $e->getMessage());
}