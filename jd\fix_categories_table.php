<?php
// 修复categories表，添加is_active字段
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // 检查字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'is_active'");
    $columnExists = $stmt->fetch();
    
    if (!$columnExists) {
        // 添加is_active字段
        $sql = "ALTER TABLE categories ADD COLUMN is_active TINYINT(1) DEFAULT 1";
        $pdo->exec($sql);
        echo "成功添加is_active字段到categories表！";
    } else {
        echo "is_active字段已存在，无需修复。";
    }
    
} catch(PDOException $e) {
    die("修复失败: " . $e->getMessage());
}

echo "<br><a href='admin/categories.php'>返回分类管理</a>";
?>